import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Cloud, Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import { Invoice } from '@/types/invoice';
import { fetchInvoicePdfBlob, generateInvoicePdfBlob } from '@/utils/pdfUtils';

interface WorkingOneDriveButtonProps {
  invoice: Invoice;
  className?: string;
}

const WorkingOneDriveButton: React.FC<WorkingOneDriveButtonProps> = ({ invoice, className = '' }) => {
  const [isUploading, setIsUploading] = useState(false);
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleOneDriveUpload = async () => {
    setIsUploading(true);
    setStatus('idle');

    try {
      console.log('🚀 Starting OneDrive upload for invoice:', invoice.id);

      // Step 1: Check if already authenticated
      let accessToken = localStorage.getItem('onedrive_access_token');

      if (!accessToken) {
        console.log('🔐 No access token found, starting device code authentication...');
        
        // Start device code flow
        const deviceResponse = await fetch('/api/onedrive/device-code', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' }
        });
        
        if (!deviceResponse.ok) {
          throw new Error(`Device code request failed: ${deviceResponse.status}`);
        }
        
        const deviceData = await deviceResponse.json();
        console.log('📱 Device code response:', deviceData);
        
        if (!deviceData.success) {
          throw new Error(deviceData.error || 'Failed to start device code flow');
        }

        const { device_code, user_code, verification_uri, interval = 5 } = deviceData;

        // Copy code to clipboard
        try {
          await navigator.clipboard.writeText(user_code);
          console.log('📋 Code copied to clipboard');
        } catch (e) {
          console.log('⚠️ Could not copy to clipboard');
        }

        // Show instructions to user
        const proceed = window.confirm(
          `OneDrive Authentication Required\n\n` +
          `STEP 1: Code copied to clipboard: ${user_code}\n` +
          `STEP 2: Click OK to open Microsoft verification page\n` +
          `STEP 3: Paste the code and sign in with your Microsoft account\n` +
          `STEP 4: Return here - upload will continue automatically\n\n` +
          `Code: ${user_code}\n\n` +
          `Click OK to continue or Cancel to abort.`
        );

        if (!proceed) {
          throw new Error('Authentication cancelled by user');
        }

        // Open verification page
        window.open(verification_uri, '_blank');

        toast.info('Authentication in Progress', {
          description: `Enter code ${user_code} on the Microsoft page. This window will update automatically.`,
          duration: 15000
        });

        // Poll for token with better error handling
        let pollCount = 0;
        const maxPolls = Math.floor((15 * 60) / interval); // 15 minutes
        let consecutiveErrors = 0;

        toast.info('Waiting for Authentication', {
          description: `Please complete authentication in the browser tab. Polling every ${interval} seconds...`,
          duration: 10000
        });

        while (pollCount < maxPolls && !accessToken) {
          await new Promise(resolve => setTimeout(resolve, interval * 1000));
          pollCount++;

          console.log(`🔄 Polling attempt ${pollCount}/${maxPolls} (${Math.floor(pollCount * interval / 60)}m ${(pollCount * interval) % 60}s elapsed)...`);

          try {
            const tokenResponse = await fetch('/api/onedrive/device-token', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ device_code })
            });

            if (!tokenResponse.ok) {
              console.error(`Token request failed: ${tokenResponse.status} ${tokenResponse.statusText}`);
              consecutiveErrors++;
              if (consecutiveErrors > 3) {
                throw new Error(`Multiple consecutive HTTP errors: ${tokenResponse.status}`);
              }
              continue;
            }

            const tokenData = await tokenResponse.json();
            console.log(`📡 Poll ${pollCount} response:`, tokenData);

            // Reset consecutive errors on successful response
            consecutiveErrors = 0;

            if (tokenData.success && tokenData.access_token) {
              accessToken = tokenData.access_token;
              localStorage.setItem('onedrive_access_token', accessToken);
              console.log('✅ Authentication successful!');

              toast.success('OneDrive Authentication Successful!', {
                description: 'Authentication completed! Proceeding with upload...'
              });
              break;
            } else if (tokenData.error === 'authorization_declined') {
              throw new Error('Authentication was declined by user');
            } else if (tokenData.error === 'expired_token') {
              throw new Error('Authentication code expired. Please try again.');
            } else if (tokenData.error === 'unauthorized') {
              throw new Error('Authentication configuration error. Please check Azure app settings.');
            } else if (tokenData.error === 'authorization_pending') {
              // This is expected - user hasn't completed auth yet
              if (pollCount % 10 === 0) { // Show progress every 10 polls
                toast.info('Still waiting for authentication...', {
                  description: `${Math.floor(pollCount * interval / 60)} minutes elapsed. Please complete authentication in browser.`,
                  duration: 5000
                });
              }
              continue;
            } else {
              console.error('Unexpected token error:', tokenData);
              throw new Error(tokenData.message || tokenData.error || 'Authentication failed');
            }
          } catch (pollError) {
            console.error('Error during token polling:', pollError);
            consecutiveErrors++;

            if (consecutiveErrors > 5) {
              throw new Error('Too many consecutive polling errors: ' + (pollError instanceof Error ? pollError.message : String(pollError)));
            }

            // Continue polling for network errors
            if (pollError instanceof TypeError && pollError.message.includes('fetch')) {
              console.log('Network error, continuing to poll...');
              continue;
            }

            // Throw for authentication errors
            if (pollError instanceof Error && !pollError.message.includes('HTTP')) {
              throw pollError;
            }
          }
        }

        if (!accessToken) {
          throw new Error('Authentication timeout. Please try again.');
        }
      } else {
        console.log('✅ Using existing access token');
      }

      // Step 2: Generate PDF using the same logic as the working OneDrive button
      console.log('📄 Generating PDF for invoice...');

      let pdfBlob: Blob;

      try {
        console.log('🔄 Attempting to fetch PDF from backend API for invoice:', invoice.id);
        // First try to fetch from backend API using the utility function
        // This ensures we get the properly formatted PDF with all fields populated
        pdfBlob = await fetchInvoicePdfBlob(invoice.id);
        console.log('✅ Successfully fetched PDF from backend API, size:', pdfBlob.size, 'bytes');

        // Validate that the PDF is not empty or corrupted
        if (pdfBlob.size < 1000) {
          throw new Error('PDF appears to be too small or corrupted');
        }
      } catch (backendError) {
        console.warn('⚠️ Backend PDF fetch failed, will NOT fallback to client-side generation:', backendError);
        throw new Error('Backend PDF generation failed. Please ensure the invoice data is complete and try again.');
      }

      // Step 3: Upload to OneDrive
      console.log('☁️ Uploading to OneDrive...');
      
      const formData = new FormData();
      formData.append('file', pdfBlob, `Invoice_${invoice.id}.pdf`);
      formData.append('invoiceNumber', invoice.id);

      const uploadResponse = await fetch('/api/onedrive/upload-pdf', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`
        },
        body: formData
      });

      if (!uploadResponse.ok) {
        const errorText = await uploadResponse.text();
        throw new Error(`Upload failed: ${uploadResponse.status} ${uploadResponse.statusText} - ${errorText}`);
      }

      const uploadResult = await uploadResponse.json();
      console.log('📤 Upload result:', uploadResult);

      if (uploadResult.success) {
        setStatus('success');
        toast.success('Invoice saved to OneDrive!', {
          description: `File: ${uploadResult.fileName || 'Invoice_' + invoice.id + '.pdf'}`,
          action: uploadResult.webUrl ? {
            label: 'Open in OneDrive',
            onClick: () => window.open(uploadResult.webUrl, '_blank')
          } : undefined
        });
      } else {
        throw new Error(uploadResult.error || uploadResult.message || 'Upload failed');
      }

    } catch (error) {
      console.error('❌ OneDrive upload failed:', error);
      setStatus('error');
      
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      toast.error('Failed to save to OneDrive', {
        description: errorMessage,
        duration: 8000
      });
    } finally {
      setIsUploading(false);
    }
  };

  const getIcon = () => {
    if (isUploading) return <Loader2 className="h-4 w-4 animate-spin" />;
    if (status === 'success') return <CheckCircle className="h-4 w-4 text-green-600" />;
    if (status === 'error') return <AlertCircle className="h-4 w-4 text-red-600" />;
    return <Cloud className="h-4 w-4" />;
  };

  const getButtonText = () => {
    if (isUploading) return 'Saving...';
    if (status === 'success') return 'Saved to OneDrive';
    return 'Save to OneDrive';
  };

  return (
    <Button
      onClick={handleOneDriveUpload}
      disabled={isUploading}
      variant="outline"
      size="sm"
      className={`gap-2 ${className}`}
    >
      {getIcon()}
      {getButtonText()}
    </Button>
  );
};

export default WorkingOneDriveButton;
