<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: white;
            color: black;
            font-size: 12px;
            line-height: 1.4;
        }

        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            position: relative;
            background: white;
        }

        /* RedBeryl Logo */
        .logo-container {
            position: absolute;
            top: 10px;
            right: 20px;
            width: 200px;
            height: 80px;
            text-align: right;
        }

        .logo {
            display: inline-block;
            padding: 10px 15px;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            border-radius: 8px;
            color: white;
            text-decoration: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .logo-text {
            font-size: 18px;
            font-weight: bold;
            margin: 0;
            letter-spacing: 1px;
        }

        .logo-tagline {
            font-size: 10px;
            margin: 2px 0 0 0;
            opacity: 0.9;
            font-style: italic;
        }

        .invoice-header {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            margin: 60px 0 30px 0;
            text-decoration: underline;
            color: #2c3e50;
        }

        .invoice-details-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            font-size: 12px;
        }

        .invoice-details-left, .invoice-details-right {
            width: 48%;
        }

        .invoice-details-title {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 13px;
            color: #2c3e50;
        }

        .invoice-details-row {
            margin-bottom: 3px;
            line-height: 1.3;
        }

        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 11px;
        }

        .invoice-table th, .invoice-table td {
            border: 1px solid #000;
            padding: 8px 6px;
            text-align: center;
            vertical-align: middle;
        }

        .invoice-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            font-size: 10px;
        }

        .invoice-table td {
            font-size: 11px;
        }

        .gst-info {
            margin: 15px 0;
            padding: 8px;
            background-color: #e8f5e8;
            border-left: 4px solid #28a745;
            font-size: 12px;
        }

        .net-payable {
            font-weight: bold;
            margin: 15px 0;
            font-size: 13px;
            color: #2c3e50;
        }

        .payment-info-section {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 11px;
        }

        .payment-info-header {
            background-color: #f8f9fa;
            font-weight: bold;
            padding: 10px;
            border: 1px solid #000;
            text-align: center;
            font-size: 12px;
        }

        .payment-info {
            width: 60%;
            vertical-align: top;
            padding: 10px;
            border: 1px solid #000;
        }

        .payment-info-row {
            display: flex;
            margin-bottom: 4px;
            align-items: flex-start;
        }

        .payment-info-label-text {
            font-weight: bold;
            width: 100px;
            flex-shrink: 0;
            font-size: 11px;
        }

        .payment-info-value {
            flex: 1;
            font-size: 11px;
        }

        .authorized-signatory {
            width: 40%;
            text-align: center;
            vertical-align: middle;
            padding: 10px;
            border: 1px solid #000;
        }

        .signatory-text {
            font-weight: bold;
            font-size: 12px;
            margin-top: 60px;
        }

        .thank-you {
            text-align: center;
            margin-top: 20px;
            font-style: italic;
            font-size: 12px;
            color: #666;
        }

        @media print {
            body { 
                margin: 0; 
                padding: 10px;
                font-size: 11px;
            }
            .invoice-container { 
                padding: 10px;
            }
            .invoice-header {
                margin: 40px 0 20px 0;
                font-size: 22px;
            }
            .logo-container {
                top: 5px;
                right: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- RedBeryl Logo -->
        <div class="logo-container">
            <div class="logo">
                <div class="logo-text">RedBeryl</div>
                <div class="logo-tagline">Tech Solutions Pvt. Ltd.</div>
                <div class="logo-tagline">Integrates Business With Technology</div>
            </div>
        </div>

        <div class="invoice-header">INVOICE</div>

        <div class="invoice-details-section">
            <div class="invoice-details-left">
                <div class="invoice-details-title">Invoice Details :-</div>
                <div class="invoice-details-row">Invoice Date: <span th:text="${invoiceDate}">07/13/2025</span></div>
                <div class="invoice-details-row">Invoice No.: <span th:text="${invoiceNumber}">RB/25-26/001</span></div>
                <div class="invoice-details-row">Invoice Month: <span th:text="${invoiceMonth}">JULY 2025</span></div>
                <div class="invoice-details-row">Invoice For: Services</div>
                <div class="invoice-details-row">HSN No.: 998313</div>
                <div class="invoice-details-row">Employee Name: <span th:text="${employeeName}">Prathamesh Kadam</span></div>
                <div class="invoice-details-row">Employee Engagement Code: ENG-0018</div>
            </div>

            <div class="invoice-details-right">
                <div class="invoice-details-title">Billed To :-</div>
                <div class="invoice-details-row"><span th:text="${clientName}">saurabh</span></div>
                <div class="invoice-details-row">GST No:</div>
            </div>
        </div>

        <table class="invoice-table">
            <thead>
                <tr>
                    <th rowspan="2">Employee<br>Name</th>
                    <th rowspan="2">Joining<br>Date</th>
                    <th rowspan="2">Rate</th>
                    <th rowspan="2">Bill<br>Amount</th>
                    <th colspan="3">GST</th>
                    <th rowspan="2">Total Bill<br>Amount</th>
                </tr>
                <tr>
                    <th>CGST<br>@9%</th>
                    <th>SGST<br>@9%</th>
                    <th>IGST<br>@18%</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td rowspan="2"><span th:text="${employeeName}">Prathamesh<br>Kadam</span></td>
                    <td rowspan="2"></td>
                    <td>₹20,000.00</td>
                    <td>₹8,666.67</td>
                    <td>₹780.00</td>
                    <td>₹780.00</td>
                    <td></td>
                    <td><span th:text="${totalAmount}">₹10,226.67</span></td>
                </tr>
            </tbody>
        </table>

        <div class="gst-info">
            <strong>GST Type:</strong>
            <span style="color: #28a745;">Intra-State (Maharashtra) - CGST (9%) + SGST (9%) = 18%</span>
        </div>

        <div class="net-payable">
            Net Payable: <span th:text="${totalAmount}">₹10,226.67</span> /- (Ten Thousand Two Hundred Twenty Six and Sixty Seven Paisa)
        </div>

        <table class="payment-info-section">
            <tr>
                <th class="payment-info-header">Payment Information</th>
                <th class="payment-info-header">Authorized Signatory</th>
            </tr>
            <tr>
                <td class="payment-info">
                    <div class="payment-info-row">
                        <div class="payment-info-label-text">Bank Name:</div>
                        <div class="payment-info-value">HDFC Bank</div>
                    </div>
                    <div class="payment-info-row">
                        <div class="payment-info-label-text">Branch Name:</div>
                        <div class="payment-info-value">MG Road Branch</div>
                    </div>
                    <div class="payment-info-row">
                        <div class="payment-info-label-text">Account Name:</div>
                        <div class="payment-info-value">Acme Corporation Pvt Ltd</div>
                    </div>
                    <div class="payment-info-row">
                        <div class="payment-info-label-text">Account No:</div>
                        <div class="payment-info-value">************</div>
                    </div>
                    <div class="payment-info-row">
                        <div class="payment-info-label-text">IFSC Code:</div>
                        <div class="payment-info-value">HDFC0001234</div>
                    </div>
                    <div class="payment-info-row">
                        <div class="payment-info-label-text">Account Type:</div>
                        <div class="payment-info-value">Current</div>
                    </div>
                    <div class="payment-info-row">
                        <div class="payment-info-label-text">GSTN:</div>
                        <div class="payment-info-value">29**********2Z5</div>
                    </div>
                    <div class="payment-info-row">
                        <div class="payment-info-label-text">CIN:</div>
                        <div class="payment-info-value">U12345KA2020PTC012345</div>
                    </div>
                    <div class="payment-info-row">
                        <div class="payment-info-label-text">PAN No:</div>
                        <div class="payment-info-value">**********</div>
                    </div>
                </td>
                <td class="authorized-signatory">
                    <div class="signatory-text">For RedBeryl Tech Solutions Pvt. Ltd.</div>
                </td>
            </tr>
        </table>

        <div class="thank-you">
            Thank you for doing business with us.
        </div>
    </div>
</body>
</html>
