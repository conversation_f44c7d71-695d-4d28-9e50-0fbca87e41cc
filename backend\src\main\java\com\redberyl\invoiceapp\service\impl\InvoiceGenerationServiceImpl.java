package com.redberyl.invoiceapp.service.impl;

import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.redberyl.invoiceapp.dto.BdmDto;
import com.redberyl.invoiceapp.dto.CandidateDto;
import com.redberyl.invoiceapp.dto.ClientDto;
import com.redberyl.invoiceapp.dto.InvoiceDto;
import com.redberyl.invoiceapp.dto.ProjectDto;
import com.redberyl.invoiceapp.dto.RedberylAccountDto;
import com.redberyl.invoiceapp.dto.SpocDto;
import com.redberyl.invoiceapp.entity.Bdm;
import com.redberyl.invoiceapp.entity.Candidate;
import com.redberyl.invoiceapp.entity.Client;
import com.redberyl.invoiceapp.entity.Invoice;
import com.redberyl.invoiceapp.entity.Project;
import com.redberyl.invoiceapp.entity.RedberylAccount;
import com.redberyl.invoiceapp.entity.Spoc;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.repository.InvoiceRepository;
import com.redberyl.invoiceapp.repository.RedberylAccountRepository;
import com.redberyl.invoiceapp.service.InvoiceGenerationService;
import com.redberyl.invoiceapp.service.InvoiceTemplateConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.context.annotation.Primary;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

/**
 * Implementation of the InvoiceGenerationService
 */
@Service
@Primary
public class InvoiceGenerationServiceImpl implements InvoiceGenerationService {

    @Autowired
    private TemplateEngine templateEngine;

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Autowired
    private RedberylAccountRepository redberylAccountRepository;

    @Autowired
    private InvoiceTemplateConfigService configService;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("MM/dd/yyyy");
    private static final NumberFormat CURRENCY_FORMATTER = NumberFormat.getCurrencyInstance(new Locale("en", "IN"));
    private static final NumberFormat NUMBER_FORMATTER = NumberFormat.getNumberInstance(new Locale("en", "IN"));

    // Create a custom formatter for amounts without currency symbol
    private static final NumberFormat AMOUNT_FORMATTER;
    static {
        AMOUNT_FORMATTER = NumberFormat.getNumberInstance(new Locale("en", "IN"));
        AMOUNT_FORMATTER.setMinimumFractionDigits(2);
        AMOUNT_FORMATTER.setMaximumFractionDigits(2);
    }

    // Arrays for number to words conversion
    private static final String[] units = {"", "One", "Two", "Three", "Four", "Five", "Six", "Seven", "Eight", "Nine", "Ten",
                                          "Eleven", "Twelve", "Thirteen", "Fourteen", "Fifteen", "Sixteen", "Seventeen", "Eighteen", "Nineteen"};
    private static final String[] tens = {"", "", "Twenty", "Thirty", "Forty", "Fifty", "Sixty", "Seventy", "Eighty", "Ninety"};

    /**
     * Format invoice number in financial year format (RB/YY-YY/XXX)
     */
    private String formatInvoiceNumber(String invoiceId, LocalDate invoiceDate) {
        try {
            String numericPart;

            // Check if it's already in INV-XXX format
            if (invoiceId.startsWith("INV-")) {
                // Extract only the part after "INV-"
                numericPart = invoiceId.substring(4);
                // Remove any non-digits that might be there
                numericPart = numericPart.replaceAll("[^\\d]", "");
            } else {
                // If it's just a number (like database ID), use it directly
                numericPart = invoiceId.replaceAll("[^\\d]", "");
            }

            // Ensure it's at least 3 digits with leading zeros
            if (numericPart.length() < 3) {
                numericPart = String.format("%03d", Integer.parseInt(numericPart));
            } else if (numericPart.length() > 3) {
                // If it's longer than 3 digits, take only the last 3 digits
                // This handles cases where database ID gets mixed with invoice number
                numericPart = numericPart.substring(numericPart.length() - 3);
            }

            // Determine financial year (April to March)
            int month = invoiceDate.getMonthValue();
            int year = invoiceDate.getYear();

            int fyStart, fyEnd;
            if (month >= 4) {
                // April to December - current FY
                fyStart = year;
                fyEnd = year + 1;
            } else {
                // January to March - previous FY
                fyStart = year - 1;
                fyEnd = year;
            }

            // Format as RB/YY-YY/XXX
            String fyStartShort = String.valueOf(fyStart).substring(2);
            String fyEndShort = String.valueOf(fyEnd).substring(2);

            return String.format("RB/%s-%s/%s", fyStartShort, fyEndShort, numericPart);
        } catch (Exception e) {
            System.err.println("Error formatting invoice number: " + e.getMessage());
            return invoiceId; // Fallback to original ID
        }
    }

    @Override
    public Resource generatePdfInvoice(Invoice invoice) {
        try {
            // Log invoice details for debugging
            System.out.println("Generating PDF for invoice: " + invoice.getInvoiceNumber());
            System.out.println("Client: " + (invoice.getClient() != null ? invoice.getClient().getName() : "NULL"));
            System.out.println("Project: " + (invoice.getProject() != null ? invoice.getProject().getName() : "NULL"));
            System.out.println("Candidate: " + (invoice.getCandidate() != null ? invoice.getCandidate().getName() : "NULL"));
            System.out.println("Total Amount: " + invoice.getTotalAmount());

            String htmlContent = generateHtmlInvoice(invoice);
            return convertHtmlToPdfResource(htmlContent);
        } catch (Exception e) {
            System.err.println("Error generating PDF invoice: " + e.getMessage());
            e.printStackTrace();
            throw new CustomException("Failed to generate PDF invoice: " + e.getMessage(), e);
        }
    }

    @Override
    public Resource generatePdfInvoiceFromDto(InvoiceDto invoiceDto) {
        try {
            // Validate that we have minimum required data
            if (invoiceDto == null) {
                throw new IllegalArgumentException("Invoice DTO cannot be null");
            }
            if (invoiceDto.getInvoiceNumber() == null || invoiceDto.getInvoiceNumber().trim().isEmpty()) {
                throw new IllegalArgumentException("Invoice number is required");
            }

            String htmlContent = generateHtmlInvoiceFromDto(invoiceDto);

            // Validate HTML content is not empty
            if (htmlContent == null || htmlContent.trim().isEmpty()) {
                throw new IllegalStateException("Generated HTML content is empty");
            }

            Resource pdfResource = convertHtmlToPdfResource(htmlContent);

            // Validate PDF resource
            if (pdfResource == null) {
                throw new IllegalStateException("Generated PDF resource is null");
            }

            try {
                long contentLength = pdfResource.contentLength();
                System.out.println("Generated PDF size: " + contentLength + " bytes");
                if (contentLength < 1000) {
                    throw new IllegalStateException("Generated PDF appears to be too small: " + contentLength + " bytes");
                }
            } catch (Exception e) {
                System.err.println("Warning: Could not validate PDF size: " + e.getMessage());
            }

            return pdfResource;
        } catch (Exception e) {
            System.err.println("Error generating PDF from DTO: " + e.getMessage());
            e.printStackTrace();
            throw new CustomException("Failed to generate PDF invoice from DTO: " + e.getMessage(), e);
        }
    }

    @Override
    public String generateHtmlInvoice(Invoice invoice) {
        Context context = new Context();
        populateContextFromInvoice(context, invoice);
        return templateEngine.process("invoice-template-clean.html", context);
    }

    @Override
    public String generateHtmlInvoiceFromDto(InvoiceDto invoiceDto) {
        Context context = new Context();
        populateContextFromDto(context, invoiceDto);

        // Log key fields to ensure they are populated
        System.out.println("=== Invoice Template Context ===");
        System.out.println("Invoice Number: " + context.getVariable("invoiceNumber"));
        System.out.println("Client Name: " + context.getVariable("clientName"));
        System.out.println("Candidate Name: " + context.getVariable("candidateName"));
        System.out.println("Billing Amount: " + context.getVariable("billingAmount"));
        System.out.println("Total Amount: " + context.getVariable("totalAmount"));
        System.out.println("================================");

        return templateEngine.process("invoice-template-clean.html", context);
    }

    /**
     * Convert HTML content to a PDF Resource
     */
    private Resource convertHtmlToPdfResource(String htmlContent) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        PdfWriter writer = new PdfWriter(outputStream);
        PdfDocument pdf = new PdfDocument(writer);
        ConverterProperties properties = new ConverterProperties();

        HtmlConverter.convertToPdf(htmlContent, pdf, properties);

        byte[] bytes = outputStream.toByteArray();
        return new ByteArrayResource(bytes);
    }

    /**
     * Populate the Thymeleaf context with data from an Invoice entity
     */
    private void populateContextFromInvoice(Context context, Invoice invoice) {
        // Company information - Load from dynamic configuration
        context.setVariable("companyName", configService.getConfigValue("company.name", "RedBeryl Tech Solutions"));
        context.setVariable("companyAddress", configService.getConfigValue("company.address", "507-B Amanora Chambers"));
        context.setVariable("companyCity", configService.getConfigValue("company.city", "Amanora Mall, Hadapsar, Pune 411028"));
        context.setVariable("companyPhone", configService.getConfigValue("company.phone", "+91 **********"));
        context.setVariable("companyEmail", configService.getConfigValue("company.email", "<EMAIL>"));
        context.setVariable("companyWebsite", configService.getConfigValue("company.website", "www.redberyl.com"));
        context.setVariable("companyGstn", configService.getConfigValue("company.gstn", "27**********1Z5"));
        context.setVariable("companyPan", configService.getConfigValue("company.pan", "**********"));
        context.setVariable("companyCin", configService.getConfigValue("company.cin", "U72200PN2020PTC123456"));

        // Invoice details
        context.setVariable("invoiceNumber", formatInvoiceNumber(invoice.getInvoiceNumber(), invoice.getInvoiceDate()));
        context.setVariable("invoiceDate", invoice.getInvoiceDate().format(DATE_FORMATTER));

        // Invoice month (derived from invoice date)
        context.setVariable("invoiceMonth", invoice.getInvoiceDate().getMonth().toString() + " " + invoice.getInvoiceDate().getYear());

        // Invoice for (default to "Services" if not available)
        context.setVariable("invoiceFor", "Services");

        // Due date
        if (invoice.getDueDate() != null) {
            context.setVariable("dueDate", invoice.getDueDate().format(DATE_FORMATTER));
        }

        // Client information
        if (invoice.getClient() != null) {
            Client client = invoice.getClient();
            context.setVariable("clientName", client.getName() != null ? client.getName() : "Unknown Client");
            context.setVariable("client", client);

            // Get GST number from project if available
            if (invoice.getProject() != null && invoice.getProject().getGstNumber() != null) {
                context.setVariable("clientGstNumber", invoice.getProject().getGstNumber());
            } else {
                context.setVariable("clientGstNumber", "");
            }
        } else {
            context.setVariable("clientName", "Unknown Client");
            context.setVariable("clientGstNumber", "");
        }

        // Project information
        if (invoice.getProject() != null) {
            Project project = invoice.getProject();
            context.setVariable("projectName", project.getName() != null ? project.getName() : "Unknown Project");
            context.setVariable("project", project);
            context.setVariable("projectBillingAddress", project.getBillingAddress() != null ? project.getBillingAddress() : "");
            // BDM information
            if (project.getBdm() != null) {
                Bdm bdm = project.getBdm();
                context.setVariable("bdmName", bdm.getName());
                context.setVariable("bdmEmail", bdm.getEmail());
                context.setVariable("bdmPhone", bdm.getPhone());
                context.setVariable("bdm", bdm);
            }

            // SPOC information
            if (project.getManagerSpoc() != null) {
                Spoc managerSpoc = project.getManagerSpoc();
                context.setVariable("managerSpoc", managerSpoc);
                context.setVariable("managerSpocName", managerSpoc.getName());
                // Use emailId instead of email for Spoc entity
                context.setVariable("managerSpocEmail", managerSpoc.getEmailId());
                // Use contactNo instead of phone for Spoc entity
                context.setVariable("managerSpocPhone", managerSpoc.getContactNo());
            }
            if (project.getAccountHeadSpoc() != null) {
                Spoc accountHeadSpoc = project.getAccountHeadSpoc();
                context.setVariable("accountHeadSpoc", accountHeadSpoc);
                context.setVariable("accountHeadSpocName", accountHeadSpoc.getName());
                context.setVariable("accountHeadSpocEmail", accountHeadSpoc.getEmailId());
                context.setVariable("accountHeadSpocPhone", accountHeadSpoc.getContactNo());
            }
            if (project.getBusinessHeadSpoc() != null) {
                Spoc businessHeadSpoc = project.getBusinessHeadSpoc();
                context.setVariable("businessHeadSpoc", businessHeadSpoc);
                context.setVariable("businessHeadSpocName", businessHeadSpoc.getName());
                context.setVariable("businessHeadSpocEmail", businessHeadSpoc.getEmailId());
                context.setVariable("businessHeadSpocPhone", businessHeadSpoc.getContactNo());
            }
            if (project.getHrSpoc() != null) {
                Spoc hrSpoc = project.getHrSpoc();
                context.setVariable("hrSpoc", hrSpoc);
                context.setVariable("hrSpocName", hrSpoc.getName());
                context.setVariable("hrSpocEmail", hrSpoc.getEmailId());
                context.setVariable("hrSpocPhone", hrSpoc.getContactNo());
            }
            if (project.getFinanceSpoc() != null) {
                Spoc financeSpoc = project.getFinanceSpoc();
                context.setVariable("financeSpoc", financeSpoc);
                context.setVariable("financeSpocName", financeSpoc.getName());
                context.setVariable("financeSpocEmail", financeSpoc.getEmailId());
                context.setVariable("financeSpocPhone", financeSpoc.getContactNo());
            }
        } else {
            context.setVariable("projectName", "Unknown Project");
            context.setVariable("projectBillingAddress", "");
        }

        // Candidate information
        if (invoice.getCandidate() != null) {
            Candidate candidate = invoice.getCandidate();
            context.setVariable("candidateName", candidate.getName() != null ? candidate.getName() : "Unknown Employee");
            context.setVariable("candidate", candidate);

            // Generate employee engagement code from candidate ID if not available
            String engagementCode = "ENG-" + String.format("%04d", candidate.getId());
            context.setVariable("employeeEngagementCode", engagementCode);

            // Candidate joining date
            if (candidate.getJoiningDate() != null) {
                context.setVariable("candidateJoiningDate", candidate.getJoiningDate().format(DATE_FORMATTER));
            } else {
                context.setVariable("candidateJoiningDate", "");
            }

            // Use rate from invoice if available, otherwise calculate based on staffing type and billing structure
            BigDecimal displayRate = BigDecimal.ZERO;

            if (invoice.getRate() != null && invoice.getRate().compareTo(BigDecimal.ZERO) > 0) {
                // Use the rate field from the invoice
                displayRate = invoice.getRate();
            } else {
                // Fallback to old calculation logic
                String staffingTypeName = invoice.getStaffingType() != null ? invoice.getStaffingType().getName() : "";

                // Determine if this is a monthly or daily billing structure
                boolean isMonthlyBilling = staffingTypeName.equalsIgnoreCase("Full-time") ||
                                         staffingTypeName.equalsIgnoreCase("Contract") ||
                                         staffingTypeName.toLowerCase().contains("monthly");

                if (isMonthlyBilling) {
                    // For monthly billing, show the monthly rate (candidate's billing rate)
                    if (candidate.getBillingRate() != null) {
                        displayRate = candidate.getBillingRate();
                    } else if (invoice.getBillingAmount() != null) {
                        // If no candidate billing rate, use the billing amount as monthly rate
                        displayRate = invoice.getBillingAmount();
                    }
                } else {
                    // For daily/hourly billing, calculate daily rate from billing amount and attendance days
                    if (invoice.getAttendanceDays() != null && invoice.getAttendanceDays() > 0 && invoice.getBillingAmount() != null) {
                        displayRate = invoice.getBillingAmount().divide(new BigDecimal(invoice.getAttendanceDays()), 2, RoundingMode.HALF_UP);
                    } else if (candidate.getBillingRate() != null) {
                        // Fallback to candidate's billing rate
                        displayRate = candidate.getBillingRate();
                    }
                }
            }

            // Format rate with appropriate billing period suffix
            String rateDisplay;
            String staffingTypeName = invoice.getStaffingType() != null ? invoice.getStaffingType().getName() : "";
            boolean isMonthlyBilling = staffingTypeName.equalsIgnoreCase("Full-time") ||
                                     staffingTypeName.equalsIgnoreCase("Contract") ||
                                     staffingTypeName.toLowerCase().contains("monthly");

            if (isMonthlyBilling) {
                rateDisplay = CURRENCY_FORMATTER.format(displayRate);
            } else {
                rateDisplay = CURRENCY_FORMATTER.format(displayRate);
            }

            context.setVariable("candidateRate", rateDisplay);
        } else {
            context.setVariable("candidateName", "Unknown Employee");
            context.setVariable("employeeEngagementCode", "");
            context.setVariable("candidateJoiningDate", "");
            context.setVariable("candidateRate", CURRENCY_FORMATTER.format(0));
        }

        // HSN Code
        if (invoice.getHsnCode() != null) {
            context.setVariable("hsnCode", invoice.getHsnCode().getCode());
            context.setVariable("hsnDescription", invoice.getHsnCode().getDescription());
        } else {
            // Default HSN code for IT services
            context.setVariable("hsnCode", "998313");
            context.setVariable("hsnDescription", "IT consulting services");
        }

        // Attendance days
        if (invoice.getAttendanceDays() != null) {
            context.setVariable("attendanceDays", invoice.getAttendanceDays());
        } else {
            context.setVariable("attendanceDays", 0);
        }

        // Financial information
        BigDecimal billingAmount = invoice.getBillingAmount() != null ? invoice.getBillingAmount() : BigDecimal.ZERO;

        // GST Logic: Determine if it's intra-state (SGST+CGST) or inter-state (IGST)
        double gstRate = 0.18; // 18%
        BigDecimal gstAmount = billingAmount.multiply(new BigDecimal("0.18"));

        // Determine GST type based on client and company location
        boolean isIntraState = determineIfIntraState(invoice);

        BigDecimal cgstAmount = BigDecimal.ZERO;
        BigDecimal sgstAmount = BigDecimal.ZERO;
        BigDecimal igstAmount = BigDecimal.ZERO;

        if (isIntraState) {
            // Intra-state: Use CGST + SGST (9% each)
            cgstAmount = gstAmount.divide(new BigDecimal("2"), 2, RoundingMode.HALF_UP);
            sgstAmount = gstAmount.divide(new BigDecimal("2"), 2, RoundingMode.HALF_UP);
            System.out.println("✓ Using INTRA-STATE GST: CGST + SGST (9% each)");
        } else {
            // Inter-state: Use IGST (18%)
            igstAmount = gstAmount;
            System.out.println("✓ Using INTER-STATE GST: IGST (18%)");
        }

        // Calculate total amount correctly (billing amount + GST)
        BigDecimal totalAmount = billingAmount.add(gstAmount);

        // If invoice has a total amount, use it, otherwise use our calculated total
        if (invoice.getTotalAmount() != null && invoice.getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
            totalAmount = invoice.getTotalAmount();
        }

        context.setVariable("billingAmount", CURRENCY_FORMATTER.format(billingAmount));
        context.setVariable("taxAmount", CURRENCY_FORMATTER.format(gstAmount));
        context.setVariable("totalAmount", CURRENCY_FORMATTER.format(totalAmount));

        // Set GST amounts with proper formatting
        if (isIntraState) {
            // Intra-state: Show CGST + SGST, hide IGST
            context.setVariable("cgstAmount", CURRENCY_FORMATTER.format(cgstAmount));
            context.setVariable("sgstAmount", CURRENCY_FORMATTER.format(sgstAmount));
            context.setVariable("igstAmount", ""); // Empty for IGST
            System.out.println("✓ Template values: CGST=" + CURRENCY_FORMATTER.format(cgstAmount) +
                             ", SGST=" + CURRENCY_FORMATTER.format(sgstAmount) + ", IGST=empty");
        } else {
            // Inter-state: Show IGST, hide CGST + SGST
            context.setVariable("cgstAmount", ""); // Empty for CGST
            context.setVariable("sgstAmount", ""); // Empty for SGST
            context.setVariable("igstAmount", CURRENCY_FORMATTER.format(igstAmount));
            System.out.println("✓ Template values: CGST=empty, SGST=empty, IGST=" + CURRENCY_FORMATTER.format(igstAmount));
        }

        // Add GST type flags for template
        context.setVariable("isIntraState", isIntraState);
        context.setVariable("isInterState", !isIntraState);
        context.setVariable("showCgstSgst", isIntraState);
        context.setVariable("showIgst", !isIntraState);

        // Total amount in words
        context.setVariable("totalAmountInWords", "(" + convertToWords(totalAmount.doubleValue()) + ")");

        // Redberyl account information - FORCE LOAD FROM DATABASE
        RedberylAccount account = null;

        // First try to get from invoice relationship
        if (invoice.getRedberylAccount() != null) {
            account = invoice.getRedberylAccount();
            System.out.println("✓ Found RedBeryl Account from invoice relationship: " + account.getAccountName());
        } else {
            // If not linked to invoice, get the first available RedBeryl account from database
            System.out.println("⚠ No RedBeryl Account linked to invoice - fetching from database...");
            try {
                account = redberylAccountRepository.findAll().stream().findFirst().orElse(null);
                if (account != null) {
                    System.out.println("✓ Found RedBeryl Account from database: " + account.getAccountName());
                    // Link it to the invoice for future use
                    invoice.setRedberylAccount(account);
                    invoiceRepository.save(invoice);
                    System.out.println("✓ Linked RedBeryl Account to invoice for future use");
                }
            } catch (Exception e) {
                System.err.println("Error fetching RedBeryl Account from database: " + e.getMessage());
            }
        }

        if (account != null) {
            // Use ACTUAL database values
            System.out.println("=== USING ACTUAL DATABASE VALUES ===");
            System.out.println("Account Name: " + account.getAccountName());
            System.out.println("Account Number: " + account.getAccountNo());
            System.out.println("Bank Name: " + account.getBankName());
            System.out.println("IFSC Code: " + account.getIfscCode());
            System.out.println("GSTN: " + account.getGstn());

            context.setVariable("redberylAccountName", account.getAccountName());
            context.setVariable("redberylAccountNumber", account.getAccountNo());
            context.setVariable("redberylAccountBankName", account.getBankName());
            context.setVariable("redberylAccountIfscCode", account.getIfscCode());
            context.setVariable("redberylAccountBranchName", account.getBranchName());
            context.setVariable("redberylAccountType", account.getAccountType());
            context.setVariable("redberylAccountGstn", account.getGstn());
            context.setVariable("redberylAccountCin", account.getCin());
            context.setVariable("redberylAccountPanNo", account.getPanNo());
            context.setVariable("redberylAccount", account);
        } else {
            System.err.println("❌ CRITICAL ERROR: No RedBeryl Account found in database!");
            System.err.println("❌ Creating default RedBeryl Account record...");

            // Create a default RedBeryl account if none exists
            try {
                RedberylAccount defaultAccount = new RedberylAccount();
                defaultAccount.setAccountName("Acme Corporation Pvt Ltd");
                defaultAccount.setAccountNo("************");
                defaultAccount.setBankName("HDFC Bank");
                defaultAccount.setIfscCode("HDFC0001234");
                defaultAccount.setBranchName("MG Road Branch");
                defaultAccount.setAccountType("Current");
                defaultAccount.setGstn("29**********2Z5");
                defaultAccount.setCin("U12345KA2020PTC123456");
                defaultAccount.setPanNo("**********");

                account = redberylAccountRepository.save(defaultAccount);
                System.out.println("✓ Created default RedBeryl Account with ID: " + account.getId());

                // Link it to the invoice
                invoice.setRedberylAccount(account);
                invoiceRepository.save(invoice);

                // Set the actual database values
                context.setVariable("redberylAccountName", account.getAccountName());
                context.setVariable("redberylAccountNumber", account.getAccountNo());
                context.setVariable("redberylAccountBankName", account.getBankName());
                context.setVariable("redberylAccountIfscCode", account.getIfscCode());
                context.setVariable("redberylAccountBranchName", account.getBranchName());
                context.setVariable("redberylAccountType", account.getAccountType());
                context.setVariable("redberylAccountGstn", account.getGstn());
                context.setVariable("redberylAccountCin", account.getCin());
                context.setVariable("redberylAccountPanNo", account.getPanNo());
                context.setVariable("redberylAccount", account);

            } catch (Exception e) {
                System.err.println("❌ Failed to create default RedBeryl Account: " + e.getMessage());
                throw new RuntimeException("No RedBeryl Account found in database and failed to create one. Please create a RedBeryl Account record first.");
            }
        }
    }

    /**
     * Populate the Thymeleaf context with data from an InvoiceDto
     */
    private void populateContextFromDto(Context context, InvoiceDto invoiceDto) {
        try {
            // Company information - Load from dynamic configuration
            context.setVariable("companyName", configService.getConfigValue("company.name", "RedBeryl Tech Solutions"));
            context.setVariable("companyAddress", configService.getConfigValue("company.address", "507-B Amanora Chambers"));
            context.setVariable("companyCity", configService.getConfigValue("company.city", "Amanora Mall, Hadapsar, Pune 411028"));
            context.setVariable("companyPhone", configService.getConfigValue("company.phone", "+91 **********"));
            context.setVariable("companyEmail", configService.getConfigValue("company.email", "<EMAIL>"));

            // Company registration details
            context.setVariable("companyGstin", configService.getConfigValue("company.gstin", "27**********1Z5"));
            context.setVariable("companyCin", configService.getConfigValue("company.cin", "U72200MH2020PTC123456"));
            context.setVariable("companyPan", configService.getConfigValue("company.pan", "**********"));
            context.setVariable("companyWebsite", configService.getConfigValue("company.website", "www.redberyl.com"));
            context.setVariable("companyGstn", configService.getConfigValue("company.gstn", "27**********1Z5"));
            context.setVariable("companyPan", configService.getConfigValue("company.pan", "**********"));
            context.setVariable("companyCin", configService.getConfigValue("company.cin", "U72200PN2020PTC123456"));

            // Invoice details - format invoice number with financial year
            LocalDate invoiceDateForFormatting = LocalDate.now();
            try {
                Object dateObj = invoiceDto.getInvoiceDate();
                if (dateObj instanceof LocalDate) {
                    invoiceDateForFormatting = (LocalDate) dateObj;
                } else if (dateObj instanceof String) {
                    invoiceDateForFormatting = LocalDate.parse((String) dateObj);
                }
            } catch (Exception e) {
                // Use current date as fallback
            }
            context.setVariable("invoiceNumber", formatInvoiceNumber(invoiceDto.getInvoiceNumber(), invoiceDateForFormatting));

            // Handle invoice date safely
            LocalDate invoiceDate = LocalDate.now();
            try {
                String formattedDate;
                Object dateObj = invoiceDto.getInvoiceDate();
                if (dateObj instanceof LocalDate) {
                    invoiceDate = (LocalDate) dateObj;
                    formattedDate = invoiceDate.format(DATE_FORMATTER);
                } else if (dateObj instanceof String) {
                    invoiceDate = LocalDate.parse((String) dateObj);
                    formattedDate = invoiceDate.format(DATE_FORMATTER);
                } else {
                    formattedDate = invoiceDate.format(DATE_FORMATTER);
                }
                context.setVariable("invoiceDate", formattedDate);
            } catch (Exception e) {
                context.setVariable("invoiceDate", invoiceDate.format(DATE_FORMATTER));
            }

            // Invoice month (derived from invoice date)
            context.setVariable("invoiceMonth", invoiceDate.getMonth().toString() + " " + invoiceDate.getYear());

            // Invoice for (default to "Services" if not available)
            context.setVariable("invoiceFor", "Services");

            // Due date
            if (invoiceDto.getDueDate() != null) {
                try {
                    String formattedDueDate;
                    Object dueDateObj = invoiceDto.getDueDate();
                    if (dueDateObj instanceof LocalDate) {
                        formattedDueDate = ((LocalDate) dueDateObj).format(DATE_FORMATTER);
                    } else if (dueDateObj instanceof String) {
                        formattedDueDate = LocalDate.parse((String) dueDateObj).format(DATE_FORMATTER);
                    } else {
                        formattedDueDate = null;
                    }
                    if (formattedDueDate != null) {
                        context.setVariable("dueDate", formattedDueDate);
                    }
                } catch (Exception e) {
                    // Skip due date if it can't be parsed
                }
            }

            // Client information
            if (invoiceDto.getClient() != null) {
                ClientDto client = invoiceDto.getClient();
                context.setVariable("clientName", client.getName());
                context.setVariable("client", client);

                // Get GST number from project if available
                if (invoiceDto.getProject() != null && invoiceDto.getProject().getGstNumber() != null) {
                    context.setVariable("clientGstNumber", invoiceDto.getProject().getGstNumber());
                } else {
                    context.setVariable("clientGstNumber", "");
                }
            } else if (invoiceDto.getClientId() != null) {
                context.setVariable("clientId", invoiceDto.getClientId());
                context.setVariable("clientName", "Client");
                context.setVariable("clientGstNumber", "");
            } else {
                context.setVariable("clientName", "Client");
                context.setVariable("clientGstNumber", "");
            }

            // Project information
            if (invoiceDto.getProject() != null) {
                ProjectDto project = invoiceDto.getProject();
                context.setVariable("projectName", project.getName());
                context.setVariable("project", project);
                context.setVariable("projectBillingAddress", project.getBillingAddress() != null ? project.getBillingAddress() : "");

                // BDM information
                if (project.getBdm() != null) {
                    BdmDto bdm = project.getBdm();
                    context.setVariable("bdmName", bdm.getName());
                    context.setVariable("bdmEmail", bdm.getEmail());
                    context.setVariable("bdmPhone", bdm.getPhone());
                    context.setVariable("bdm", bdm);
                }

                // SPOC information
                if (project.getManagerSpoc() != null) {
                    SpocDto managerSpoc = project.getManagerSpoc();
                    context.setVariable("managerSpoc", managerSpoc);
                    context.setVariable("managerSpocName", managerSpoc.getName());
                    context.setVariable("managerSpocEmail", managerSpoc.getEmailId());
                    context.setVariable("managerSpocPhone", managerSpoc.getContactNo());
                }
                if (project.getAccountHeadSpoc() != null) {
                    SpocDto accountHeadSpoc = project.getAccountHeadSpoc();
                    context.setVariable("accountHeadSpoc", accountHeadSpoc);
                    context.setVariable("accountHeadSpocName", accountHeadSpoc.getName());
                    context.setVariable("accountHeadSpocEmail", accountHeadSpoc.getEmailId());
                    context.setVariable("accountHeadSpocPhone", accountHeadSpoc.getContactNo());
                }
                if (project.getBusinessHeadSpoc() != null) {
                    SpocDto businessHeadSpoc = project.getBusinessHeadSpoc();
                    context.setVariable("businessHeadSpoc", businessHeadSpoc);
                    context.setVariable("businessHeadSpocName", businessHeadSpoc.getName());
                    context.setVariable("businessHeadSpocEmail", businessHeadSpoc.getEmailId());
                    context.setVariable("businessHeadSpocPhone", businessHeadSpoc.getContactNo());
                }
                if (project.getHrSpoc() != null) {
                    SpocDto hrSpoc = project.getHrSpoc();
                    context.setVariable("hrSpoc", hrSpoc);
                    context.setVariable("hrSpocName", hrSpoc.getName());
                    context.setVariable("hrSpocEmail", hrSpoc.getEmailId());
                    context.setVariable("hrSpocPhone", hrSpoc.getContactNo());
                }
                if (project.getFinanceSpoc() != null) {
                    SpocDto financeSpoc = project.getFinanceSpoc();
                    context.setVariable("financeSpoc", financeSpoc);
                    context.setVariable("financeSpocName", financeSpoc.getName());
                    context.setVariable("financeSpocEmail", financeSpoc.getEmailId());
                    context.setVariable("financeSpocPhone", financeSpoc.getContactNo());
                }
            } else if (invoiceDto.getProjectId() != null) {
                context.setVariable("projectId", invoiceDto.getProjectId());
                context.setVariable("projectName", "Project");
                context.setVariable("projectBillingAddress", "");
            } else {
                context.setVariable("projectName", "Project");
                context.setVariable("projectBillingAddress", "");
            }

            // Candidate information
            if (invoiceDto.getCandidate() != null) {
                CandidateDto candidate = invoiceDto.getCandidate();
                context.setVariable("candidateName", candidate.getName());
                context.setVariable("candidate", candidate);

                // Generate employee engagement code from candidate ID if not available
                String engagementCode = "ENG-" + String.format("%04d", candidate.getId());
                context.setVariable("employeeEngagementCode", engagementCode);

                // Candidate joining date
                if (candidate.getJoiningDate() != null) {
                    try {
                        LocalDate joiningDate;
                        Object dateObj = candidate.getJoiningDate();
                        if (dateObj instanceof LocalDate) {
                            joiningDate = (LocalDate) dateObj;
                            context.setVariable("candidateJoiningDate", joiningDate.format(DATE_FORMATTER));
                        } else if (dateObj instanceof String) {
                            joiningDate = LocalDate.parse((String) dateObj);
                            context.setVariable("candidateJoiningDate", joiningDate.format(DATE_FORMATTER));
                        } else {
                            context.setVariable("candidateJoiningDate", "");
                        }
                    } catch (Exception e) {
                        context.setVariable("candidateJoiningDate", "");
                    }
                } else {
                    context.setVariable("candidateJoiningDate", "");
                }

                // Use rate from invoice DTO if available, otherwise calculate based on staffing type and billing structure
                BigDecimal displayRate = BigDecimal.ZERO;

                if (invoiceDto.getRate() != null && invoiceDto.getRate().compareTo(BigDecimal.ZERO) > 0) {
                    // Use the rate field from the invoice DTO
                    displayRate = invoiceDto.getRate();
                } else {
                    // Fallback to old calculation logic
                    String staffingTypeName = invoiceDto.getStaffingType() != null ? invoiceDto.getStaffingType().getName() : "";

                    // Determine if this is a monthly or daily billing structure
                    boolean isMonthlyBilling = staffingTypeName.equalsIgnoreCase("Full-time") ||
                                             staffingTypeName.equalsIgnoreCase("Contract") ||
                                             staffingTypeName.toLowerCase().contains("monthly");

                    if (isMonthlyBilling) {
                        // For monthly billing, show the monthly rate (candidate's billing rate)
                        if (candidate.getBillingRate() != null) {
                            displayRate = candidate.getBillingRate();
                        } else if (invoiceDto.getBillingAmount() != null) {
                            // If no candidate billing rate, use the billing amount as monthly rate
                            displayRate = invoiceDto.getBillingAmount();
                        }
                    } else {
                        // For daily/hourly billing, calculate daily rate from billing amount and attendance days
                        if (invoiceDto.getAttendanceDays() != null && invoiceDto.getAttendanceDays() > 0 && invoiceDto.getBillingAmount() != null) {
                            displayRate = invoiceDto.getBillingAmount().divide(new BigDecimal(invoiceDto.getAttendanceDays()), 2, RoundingMode.HALF_UP);
                        } else if (candidate.getBillingRate() != null) {
                            // Fallback to candidate's billing rate
                            displayRate = candidate.getBillingRate();
                        }
                    }
                }

                // Format rate with appropriate billing period suffix
                String rateDisplay;
                String staffingTypeName = invoiceDto.getStaffingType() != null ? invoiceDto.getStaffingType().getName() : "";
                boolean isMonthlyBilling = staffingTypeName.equalsIgnoreCase("Full-time") ||
                                         staffingTypeName.equalsIgnoreCase("Contract") ||
                                         staffingTypeName.toLowerCase().contains("monthly");

                if (isMonthlyBilling) {
                    rateDisplay = CURRENCY_FORMATTER.format(displayRate);
                } else {
                    rateDisplay = CURRENCY_FORMATTER.format(displayRate);
                }

                context.setVariable("candidateRate", rateDisplay);
            } else if (invoiceDto.getCandidateId() != null) {
                context.setVariable("candidateId", invoiceDto.getCandidateId());
                context.setVariable("candidateName", "Employee");
                context.setVariable("employeeEngagementCode", "");
                context.setVariable("candidateJoiningDate", "");
                context.setVariable("candidateRate", CURRENCY_FORMATTER.format(0));
            } else {
                context.setVariable("candidateName", "Employee");
                context.setVariable("employeeEngagementCode", "");
                context.setVariable("candidateJoiningDate", "");
                context.setVariable("candidateRate", CURRENCY_FORMATTER.format(0));
            }

            // HSN Code
            if (invoiceDto.getHsnCode() != null) {
                context.setVariable("hsnCode", invoiceDto.getHsnCode().getCode());
                context.setVariable("hsnDescription", invoiceDto.getHsnCode().getDescription());
            } else if (invoiceDto.getHsnId() != null) {
                context.setVariable("hsnCode", "998313"); // Default HSN code
                context.setVariable("hsnDescription", "IT consulting services");
            } else {
                context.setVariable("hsnCode", "998313"); // Default HSN code
                context.setVariable("hsnDescription", "IT consulting services");
            }

            // Attendance days
            if (invoiceDto.getAttendanceDays() != null) {
                context.setVariable("attendanceDays", invoiceDto.getAttendanceDays());
            } else {
                context.setVariable("attendanceDays", 0);
            }

            // Financial information
            BigDecimal billingAmount = invoiceDto.getBillingAmount() != null ? invoiceDto.getBillingAmount() : BigDecimal.ZERO;

            // GST breakup (assuming 18% total, split as 9% CGST and 9% SGST, or 18% IGST)
            BigDecimal gstAmount = billingAmount.multiply(new BigDecimal("0.18"));
            BigDecimal cgstAmount = gstAmount.divide(new BigDecimal("2"), 2, RoundingMode.HALF_UP);
            BigDecimal sgstAmount = gstAmount.divide(new BigDecimal("2"), 2, RoundingMode.HALF_UP);

            // Calculate total amount correctly (billing amount + GST)
            BigDecimal totalAmount = billingAmount.add(gstAmount);

            // If invoice has a total amount, use it, otherwise use our calculated total
            if (invoiceDto.getTotalAmount() != null && invoiceDto.getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
                totalAmount = invoiceDto.getTotalAmount();
            } else {
                // Update the DTO with the correct total amount
                invoiceDto.setTotalAmount(totalAmount);
            }

            context.setVariable("billingAmount", CURRENCY_FORMATTER.format(billingAmount));
            context.setVariable("taxAmount", CURRENCY_FORMATTER.format(gstAmount));
            context.setVariable("totalAmount", CURRENCY_FORMATTER.format(totalAmount));
            context.setVariable("cgstAmount", CURRENCY_FORMATTER.format(cgstAmount));
            context.setVariable("sgstAmount", CURRENCY_FORMATTER.format(sgstAmount));
            context.setVariable("igstAmount", CURRENCY_FORMATTER.format(gstAmount));

            // GST Type determination (assuming Maharashtra as default state)
            // For now, we'll default to intra-state (CGST + SGST)
            // This can be enhanced later to check client's state vs company state
            boolean isIntraState = true; // Default to intra-state
            boolean isInterState = !isIntraState;
            context.setVariable("isIntraState", isIntraState);
            context.setVariable("isInterState", isInterState);

            // Total amount in words
            context.setVariable("totalAmountInWords", "(" + convertToWords(totalAmount.doubleValue()) + ")");

            // Add some additional validation fields that might be missing
            if (!context.containsVariable("invoiceFor")) {
                context.setVariable("invoiceFor", "Services");
            }
            if (!context.containsVariable("attendanceDays")) {
                context.setVariable("attendanceDays", invoiceDto.getAttendanceDays() != null ? invoiceDto.getAttendanceDays() : 0);
            }

            // Redberyl account information
            if (invoiceDto.getRedberylAccount() != null) {
                RedberylAccountDto account = invoiceDto.getRedberylAccount();
                context.setVariable("redberylAccountName", account.getAccountName() != null ? account.getAccountName() : "RedBeryl Tech Solutions Pvt Ltd.");
                context.setVariable("redberylAccountNumber", account.getAccountNo() != null ? account.getAccountNo() : "**************");
                context.setVariable("redberylAccountBankName", account.getBankName() != null ? account.getBankName() : "HDFC Bank Ltd.");
                context.setVariable("redberylAccountIfscCode", account.getIfscCode() != null ? account.getIfscCode() : "HDFC0000486");
                context.setVariable("redberylAccountBranchName", account.getBranchName() != null ? account.getBranchName() : "Destination Centre, Magarpatta, Pune");
                context.setVariable("redberylAccountType", account.getAccountType() != null ? account.getAccountType() : "Current Account");
                context.setVariable("redberylAccountGstn", account.getGstn() != null ? account.getGstn() : "27**********1Z5");
                context.setVariable("redberylAccountCin", account.getCin() != null ? account.getCin() : "U72900PN2022PTC213381");
                context.setVariable("redberylAccountPanNo", account.getPanNo() != null ? account.getPanNo() : "**********");
                context.setVariable("redberylAccount", account);
            } else if (invoiceDto.getRedberylAccountId() != null) {
                context.setVariable("redberylAccountId", invoiceDto.getRedberylAccountId());
                // Try to fetch account by ID from database
                try {
                    RedberylAccount account = redberylAccountRepository.findById(invoiceDto.getRedberylAccountId()).orElse(null);
                    if (account != null) {
                        context.setVariable("redberylAccountName", account.getAccountName());
                        context.setVariable("redberylAccountNumber", account.getAccountNo());
                        context.setVariable("redberylAccountBankName", account.getBankName());
                        context.setVariable("redberylAccountIfscCode", account.getIfscCode());
                        context.setVariable("redberylAccountBranchName", account.getBranchName());
                        context.setVariable("redberylAccountType", account.getAccountType());
                        context.setVariable("redberylAccountGstn", account.getGstn());
                        context.setVariable("redberylAccountCin", account.getCin());
                        context.setVariable("redberylAccountPanNo", account.getPanNo());
                        context.setVariable("redberylAccount", account);
                    } else {
                        // Fetch any available account from database
                        setRedberylAccountFromDatabase(context);
                    }
                } catch (Exception e) {
                    System.err.println("Error fetching RedBeryl Account by ID: " + e.getMessage());
                    setRedberylAccountFromDatabase(context);
                }
            } else {
                // Fetch any available account from database
                setRedberylAccountFromDatabase(context);
            }
        } catch (Exception e) {
            // Log the error but continue with default values
            System.err.println("Error populating context from DTO: " + e.getMessage());
            e.printStackTrace();

            // Set default values for critical fields
            String fallbackInvoiceNumber = invoiceDto.getInvoiceNumber() != null ? invoiceDto.getInvoiceNumber() : "N/A";
            context.setVariable("invoiceNumber", formatInvoiceNumber(fallbackInvoiceNumber, LocalDate.now()));
            context.setVariable("invoiceDate", LocalDate.now().format(DATE_FORMATTER));
            context.setVariable("invoiceMonth", LocalDate.now().getMonth().toString() + " " + LocalDate.now().getYear());
            context.setVariable("invoiceFor", "Services");
            context.setVariable("clientName", "Client");
            context.setVariable("clientGstNumber", "");
            context.setVariable("projectName", "Project");
            context.setVariable("projectBillingAddress", "");
            context.setVariable("candidateName", "Employee");
            context.setVariable("employeeEngagementCode", "");
            context.setVariable("candidateJoiningDate", "");
            // These default values should not be needed as they are set elsewhere in the method
            // But we'll keep them as a fallback just in case
            if (!context.containsVariable("candidateRate")) {
                context.setVariable("candidateRate", CURRENCY_FORMATTER.format(0));
            }
            if (!context.containsVariable("hsnCode")) {
                context.setVariable("hsnCode", "998313");
            }
            if (!context.containsVariable("hsnDescription")) {
                context.setVariable("hsnDescription", "IT consulting services");
            }
            if (!context.containsVariable("redberylAccountName")) {
                context.setVariable("redberylAccountName", "RedBeryl Tech Solutions Pvt Ltd.");
            }
            context.setVariable("redberylAccountNumber", "**************");
            context.setVariable("redberylAccountBankName", "HDFC Bank Ltd.");
            context.setVariable("redberylAccountIfscCode", "HDFC0000486");
            context.setVariable("redberylAccountBranchName", "Destination Centre, Magarpatta, Pune");
            context.setVariable("redberylAccountType", "Current Account");
            context.setVariable("redberylAccountGstn", "27**********1Z5");
            context.setVariable("redberylAccountCin", "U72900PN2022PTC213381");
            context.setVariable("redberylAccountPanNo", "**********");
        }
    }

    /**
     * Helper method to set RedBeryl account information from database
     */
    private void setRedberylAccountFromDatabase(Context context) {
        try {
            RedberylAccount account = redberylAccountRepository.findAll().stream().findFirst().orElse(null);
            if (account != null) {
                System.out.println("✓ Using RedBeryl Account from database: " + account.getAccountName());
                context.setVariable("redberylAccountName", account.getAccountName());
                context.setVariable("redberylAccountNumber", account.getAccountNo());
                context.setVariable("redberylAccountBankName", account.getBankName());
                context.setVariable("redberylAccountIfscCode", account.getIfscCode());
                context.setVariable("redberylAccountBranchName", account.getBranchName());
                context.setVariable("redberylAccountType", account.getAccountType());
                context.setVariable("redberylAccountGstn", account.getGstn());
                context.setVariable("redberylAccountCin", account.getCin());
                context.setVariable("redberylAccountPanNo", account.getPanNo());
                context.setVariable("redberylAccount", account);
            } else {
                System.err.println("❌ No RedBeryl Account found in database - creating default account");
                // Create a default account
                RedberylAccount defaultAccount = new RedberylAccount();
                defaultAccount.setAccountName("Acme Corporation Pvt Ltd");
                defaultAccount.setAccountNo("************");
                defaultAccount.setBankName("HDFC Bank");
                defaultAccount.setIfscCode("HDFC0001234");
                defaultAccount.setBranchName("MG Road Branch");
                defaultAccount.setAccountType("Current");
                defaultAccount.setGstn("29**********2Z5");
                defaultAccount.setCin("U12345KA2020PTC123456");
                defaultAccount.setPanNo("**********");

                account = redberylAccountRepository.save(defaultAccount);
                System.out.println("✓ Created default RedBeryl Account with ID: " + account.getId());

                context.setVariable("redberylAccountName", account.getAccountName());
                context.setVariable("redberylAccountNumber", account.getAccountNo());
                context.setVariable("redberylAccountBankName", account.getBankName());
                context.setVariable("redberylAccountIfscCode", account.getIfscCode());
                context.setVariable("redberylAccountBranchName", account.getBranchName());
                context.setVariable("redberylAccountType", account.getAccountType());
                context.setVariable("redberylAccountGstn", account.getGstn());
                context.setVariable("redberylAccountCin", account.getCin());
                context.setVariable("redberylAccountPanNo", account.getPanNo());
                context.setVariable("redberylAccount", account);
            }
        } catch (Exception e) {
            System.err.println("❌ Error setting RedBeryl Account from database: " + e.getMessage());
            // Use minimal fallback values only if database operations fail completely
            context.setVariable("redberylAccountName", "Database Error - Please Check Configuration");
            context.setVariable("redberylAccountNumber", "N/A");
            context.setVariable("redberylAccountBankName", "N/A");
            context.setVariable("redberylAccountIfscCode", "N/A");
            context.setVariable("redberylAccountBranchName", "N/A");
            context.setVariable("redberylAccountType", "N/A");
            context.setVariable("redberylAccountGstn", "N/A");
            context.setVariable("redberylAccountCin", "N/A");
            context.setVariable("redberylAccountPanNo", "N/A");
        }
    }

    /**
     * Convert a number to words
     * @param number The number to convert
     * @return The number in words
     */
    private String convertToWords(double number) {
        try {
            long rupees = (long) number;
            int paise = (int) Math.round((number - rupees) * 100);

            if (rupees == 0) {
                return "Zero Rupees" + (paise > 0 ? " and " + convertPaiseToWords(paise) : "");
            }

            String rupeesInWords = convertRupeesToWords(rupees);

            if (paise > 0) {
                return rupeesInWords + " and " + convertPaiseToWords(paise);
            } else {
                return rupeesInWords + " Only";
            }
        } catch (Exception e) {
            // In case of any error, return a default message
            return "Amount in words not available";
        }
    }

    /**
     * Convert rupees to words
     * @param rupees The rupees amount
     * @return The rupees in words
     */
    private String convertRupeesToWords(long rupees) {
        if (rupees < 20) {
            return units[(int) rupees];
        }

        if (rupees < 100) {
            return tens[(int) rupees / 10] + (rupees % 10 > 0 ? " " + units[(int) rupees % 10] : "");
        }

        if (rupees < 1000) {
            return units[(int) rupees / 100] + " Hundred" + (rupees % 100 > 0 ? " " + convertRupeesToWords(rupees % 100) : "");
        }

        if (rupees < 100000) {
            return convertRupeesToWords(rupees / 1000) + " Thousand" + (rupees % 1000 > 0 ? " " + convertRupeesToWords(rupees % 1000) : "");
        }

        if (rupees < 10000000) {
            return convertRupeesToWords(rupees / 100000) + " Lakh" + (rupees % 100000 > 0 ? " " + convertRupeesToWords(rupees % 100000) : "");
        }

        return convertRupeesToWords(rupees / 10000000) + " Crore" + (rupees % 10000000 > 0 ? " " + convertRupeesToWords(rupees % 10000000) : "");
    }

    /**
     * Convert paise to words
     * @param paise The paise amount
     * @return The paise in words
     */
    private String convertPaiseToWords(int paise) {
        if (paise < 20) {
            return units[paise] + " Paise";
        }

        return tens[paise / 10] + (paise % 10 > 0 ? " " + units[paise % 10] : "") + " Paise";
    }

    /**
     * Determine if the transaction is intra-state (same state) or inter-state
     * Enhanced logic for state-wise GST calculation
     */
    private boolean determineIfIntraState(Invoice invoice) {
        try {
            // RedBeryl is located in Maharashtra (state code 27)
            String companyState = "Maharashtra";
            String companyStateCode = "27";

            // Method 1: Check project state field directly
            if (invoice.getProject() != null && invoice.getProject().getState() != null) {
                String clientState = invoice.getProject().getState().trim();
                boolean isMaharashtra = clientState.equalsIgnoreCase("Maharashtra") ||
                                       clientState.equalsIgnoreCase("MH") ||
                                       clientState.equalsIgnoreCase("Maha");

                System.out.println("✓ Using Project State Field");
                System.out.println("Client State: " + clientState);
                System.out.println("Is Maharashtra: " + isMaharashtra);

                return isMaharashtra;
            }

            // Method 2: Compare GST state codes (first 2 digits)
            String companyGstNumber = null;
            if (invoice.getRedberylAccount() != null && invoice.getRedberylAccount().getGstn() != null) {
                companyGstNumber = invoice.getRedberylAccount().getGstn();
            }

            String clientGstNumber = null;
            if (invoice.getProject() != null && invoice.getProject().getGstNumber() != null) {
                clientGstNumber = invoice.getProject().getGstNumber();
            }

            if (companyGstNumber != null && clientGstNumber != null &&
                companyGstNumber.length() >= 2 && clientGstNumber.length() >= 2) {

                String companyGstStateCode = companyGstNumber.substring(0, 2);
                String clientGstStateCode = clientGstNumber.substring(0, 2);

                boolean isSameState = companyGstStateCode.equals(clientGstStateCode);

                System.out.println("✓ Using GST State Code Comparison");
                System.out.println("Company GST State Code: " + companyGstStateCode);
                System.out.println("Client GST State Code: " + clientGstStateCode);
                System.out.println("Same State: " + isSameState);

                return isSameState;
            }

            // Method 3: Check billing address for Maharashtra keywords
            if (invoice.getProject() != null && invoice.getProject().getBillingAddress() != null) {
                String address = invoice.getProject().getBillingAddress().toLowerCase();
                boolean isMaharashtra = address.contains("pune") ||
                                       address.contains("mumbai") ||
                                       address.contains("maharashtra") ||
                                       address.contains("nagpur") ||
                                       address.contains("nashik") ||
                                       address.contains("aurangabad");

                System.out.println("✓ Using Address Analysis");
                System.out.println("Client address contains Maharashtra cities: " + isMaharashtra);
                return isMaharashtra;
            }

            // Default to intra-state (CGST + SGST) for Maharashtra clients
            System.out.println("⚠ Cannot determine state - defaulting to intra-state (CGST + SGST)");
            return true;

        } catch (Exception e) {
            System.err.println("❌ Error determining GST type: " + e.getMessage());
            e.printStackTrace();
            // Default to intra-state in case of error
            return true;
        }
    }
}
