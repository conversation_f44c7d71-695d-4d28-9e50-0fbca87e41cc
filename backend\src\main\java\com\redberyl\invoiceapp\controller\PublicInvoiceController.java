package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.service.InvoiceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Public controller for invoices that doesn't require authentication
 * This is useful for testing and development purposes
 */
@RestController
@RequestMapping("/invoices")
@Tag(name = "Public Invoice API", description = "Public API for invoices (no authentication required)")
@CrossOrigin(origins = {"http://localhost:3060", "http://127.0.0.1:3060"}, maxAge = 3600)
public class PublicInvoiceController {

    @Autowired
    private InvoiceService invoiceService;

    @DeleteMapping("/delete/{id}")
    @Operation(summary = "Delete invoice (public)", description = "Delete invoice without authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Invoice deleted successfully"),
            @ApiResponse(responseCode = "404", description = "Invoice not found"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
    })
    public ResponseEntity<Void> deleteInvoice(@PathVariable Long id) {
        try {
            invoiceService.deleteInvoice(id);
            System.out.println("PublicInvoiceController: Deleted invoice with ID: " + id);
            return ResponseEntity.noContent().build();
        } catch (Exception e) {
            System.err.println("PublicInvoiceController: Error deleting invoice with ID " + id + ": " + e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }
}
