# Network Configuration Guide

This guide explains how to configure the Invoice App to work across different machines on your network.

## Quick Setup for IP Address *************

### ✅ **Already Configured**
The application is already configured to work with IP address `*************`. Both frontend and backend are set up correctly.

### **Current Configuration**
- **Frontend**: Runs on `http://*************:3060`
- **Backend**: Runs on `http://*************:8091`
- **CORS**: Configured to allow requests from `*************:3060`

## 🚀 **How to Access from Another Laptop**

### **Step 1: Ensure Both Servers are Running**
On the main machine (*************):

```bash
# Start Backend (Terminal 1)
cd backend
mvn spring-boot:run

# Start Frontend (Terminal 2)
cd frontend
npm run dev
```

### **Step 2: Access from Another Laptop**
From any other laptop on the same network:
- Open browser and go to: `http://*************:3060`
- The application should work normally, including project creation

## 🔧 **Configuration Files**

### **Frontend Configuration**
- **File**: `frontend/vite.config.ts`
- **Environment**: `frontend/.env`
- **Current Backend Target**: `http://*************:8091`

### **Backend Configuration**
- **File**: `backend/src/main/resources/application.properties`
- **Server Address**: `0.0.0.0` (listens on all interfaces)
- **CORS**: Configured for `*************:3060`

## 🔄 **Switching Between Configurations**

### **Option 1: Use Batch Scripts (Windows)**
```bash
# Switch to IP configuration
scripts\switch-to-ip.bat

# Switch to localhost configuration
scripts\switch-to-localhost.bat
```

### **Option 2: Manual Configuration**
Edit `frontend/.env`:
```env
# For IP access
VITE_BACKEND_URL=http://*************:8091
VITE_API_URL=http://*************:8091/api

# For localhost access
VITE_BACKEND_URL=http://localhost:8091
VITE_API_URL=http://localhost:8091/api
```

## 🌐 **For Different IP Addresses**

### **To Use a Different IP Address (e.g., *************)**

1. **Update Frontend Environment**:
   ```env
   VITE_BACKEND_URL=http://*************:8091
   VITE_API_URL=http://*************:8091/api
   ```

2. **Update Backend CORS** in `backend/src/main/java/com/redberyl/invoiceapp/config/CorsConfig.java`:
   ```java
   .allowedOrigins(
       "http://localhost:3060",
       "http://127.0.0.1:3060",
       "http://*************:3060"  // Add your new IP
   )
   ```

3. **Restart Both Servers**

## 🔍 **Troubleshooting**

### **Project Creation Not Working from Another Laptop**
✅ **Fixed**: The proxy configuration now correctly routes to `*************:8091`

### **CORS Errors**
✅ **Fixed**: CORS is configured to allow `*************:3060`

### **Connection Refused**
- Ensure backend is running on port 8091
- Ensure frontend is running on port 3060
- Check Windows Firewall settings
- Verify IP address is correct

### **Check if Servers are Running**
```bash
# Check frontend
netstat -an | findstr :3060

# Check backend
netstat -an | findstr :8091
```

## 📝 **Network Requirements**

- All machines must be on the same network
- Ports 3060 and 8091 must be accessible
- Windows Firewall may need to allow these ports
- Backend server must have IP address `*************`

## ✅ **Verification Steps**

1. **From main machine**: Access `http://localhost:3060` ✅
2. **From another laptop**: Access `http://*************:3060` ✅
3. **Test project creation**: Should work from both locations ✅
4. **Test all features**: Candidates, Clients, Invoices, etc. ✅

## 🎯 **Current Status**

- ✅ Frontend configured for IP access
- ✅ Backend configured for IP access  
- ✅ CORS configured correctly
- ✅ Proxy routing fixed
- ✅ Environment variables set
- ✅ Switch scripts created

**The application is now ready for network access!**
