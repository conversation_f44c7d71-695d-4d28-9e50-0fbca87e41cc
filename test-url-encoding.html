<!DOCTYPE html>
<html>
<head>
    <title>Test URL Encoding Fix</title>
</head>
<body>
    <h1>Test URL Encoding Fix</h1>
    <div id="results"></div>
    
    <script>
        async function testUrlEncoding() {
            const resultsDiv = document.getElementById('results');
            
            // Test invoice number with slashes
            const invoiceNumber = 'INV-RB/25-26/001';
            const encodedInvoiceNumber = encodeURIComponent(invoiceNumber);
            
            resultsDiv.innerHTML += `<p><strong>Original:</strong> ${invoiceNumber}</p>`;
            resultsDiv.innerHTML += `<p><strong>Encoded:</strong> ${encodedInvoiceNumber}</p>`;
            
            // Test the debug endpoint
            try {
                const debugUrl = `/api/invoice-generation/debug/${encodedInvoiceNumber}`;
                resultsDiv.innerHTML += `<p><strong>Testing debug URL:</strong> ${debugUrl}</p>`;
                
                const response = await fetch(debugUrl);
                const data = await response.json();
                
                resultsDiv.innerHTML += `<p><strong>Debug Response:</strong> ${JSON.stringify(data, null, 2)}</p>`;
                
                if (response.ok) {
                    resultsDiv.innerHTML += `<p style="color: green;"><strong>✅ Debug endpoint works!</strong></p>`;
                } else {
                    resultsDiv.innerHTML += `<p style="color: red;"><strong>❌ Debug endpoint failed</strong></p>`;
                }
            } catch (error) {
                resultsDiv.innerHTML += `<p style="color: red;"><strong>❌ Error:</strong> ${error.message}</p>`;
            }
            
            // Test the PDF endpoint
            try {
                const pdfUrl = `/api/invoice-generation/public/pdf/by-number/${encodedInvoiceNumber}`;
                resultsDiv.innerHTML += `<p><strong>Testing PDF URL:</strong> ${pdfUrl}</p>`;
                
                const response = await fetch(pdfUrl);
                
                if (response.ok) {
                    resultsDiv.innerHTML += `<p style="color: green;"><strong>✅ PDF endpoint works!</strong></p>`;
                    resultsDiv.innerHTML += `<p><strong>PDF size:</strong> ${response.headers.get('content-length')} bytes</p>`;
                } else {
                    resultsDiv.innerHTML += `<p style="color: red;"><strong>❌ PDF endpoint failed:</strong> ${response.status} ${response.statusText}</p>`;
                }
            } catch (error) {
                resultsDiv.innerHTML += `<p style="color: red;"><strong>❌ PDF Error:</strong> ${error.message}</p>`;
            }
        }
        
        // Run test when page loads
        window.onload = testUrlEncoding;
    </script>
</body>
</html>
