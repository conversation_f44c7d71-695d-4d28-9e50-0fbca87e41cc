import React from 'react';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { Invoice } from '@/types/invoice';

// Helper function to convert numbers to words
const convertNumberToWords = (num: number): string => {
  if (num === 0) return 'Zero';

  const ones = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine'];
  const teens = ['Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];
  const tens = ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];
  const thousands = ['', 'Thousand', 'Lakh', 'Crore'];

  const convertHundreds = (n: number): string => {
    let result = '';
    if (n >= 100) {
      result += ones[Math.floor(n / 100)] + ' Hundred ';
      n %= 100;
    }
    if (n >= 20) {
      result += tens[Math.floor(n / 10)] + ' ';
      n %= 10;
    } else if (n >= 10) {
      result += teens[n - 10] + ' ';
      return result;
    }
    if (n > 0) {
      result += ones[n] + ' ';
    }
    return result;
  };

  if (num < 1000) {
    return convertHundreds(num).trim();
  }

  let result = '';
  let thousandIndex = 0;

  while (num > 0) {
    if (num % 1000 !== 0) {
      result = convertHundreds(num % 1000) + thousands[thousandIndex] + ' ' + result;
    }
    num = Math.floor(num / 1000);
    thousandIndex++;
  }

  return result.trim();
};

/**
 * Generate PDF blob from invoice data for OneDrive upload using the clean template
 */
export const generateInvoicePdfBlob = async (invoice: Invoice): Promise<Blob> => {
  try {
    console.log('🔄 Starting PDF generation for invoice:', invoice);

    // Use the original invoice data if available, otherwise use the display data
    const sourceInvoice = (invoice as any).originalInvoice || invoice;
    console.log('🔄 Using source invoice data:', sourceInvoice);

    // Transform invoice data to match PDF template expectations
    const transformedInvoice = {
      id: sourceInvoice.id?.toString() || invoice.id?.toString() || '',
      client: sourceInvoice.client?.name || invoice.client?.name || (typeof invoice.client === 'string' ? invoice.client : ''),
      project: sourceInvoice.project?.name || invoice.project?.name || (typeof invoice.project === 'string' ? invoice.project : ''),
      candidate: sourceInvoice.candidate?.name || invoice.candidate?.name || (typeof invoice.candidate === 'string' ? invoice.candidate : ''),
      invoiceType: sourceInvoice.invoiceType?.invoiceType || invoice.invoiceType?.invoiceType || '',
      staffingType: sourceInvoice.staffingType?.name || invoice.staffingType?.name || '',
      amount: sourceInvoice.billingAmount?.toString() || invoice.billingAmount?.toString() || '0',
      tax: sourceInvoice.taxAmount?.toString() || invoice.taxAmount?.toString() || '0',
      total: sourceInvoice.totalAmount?.toString() || invoice.totalAmount?.toString() || '0',
      issueDate: sourceInvoice.invoiceDate?.toString() || invoice.invoiceDate?.toString() || new Date().toISOString(),
      dueDate: sourceInvoice.dueDate?.toString() || invoice.dueDate?.toString() || new Date().toISOString(),
      status: sourceInvoice.status || invoice.status || 'DRAFT',
      recurring: sourceInvoice.isRecurring || invoice.isRecurring || false,
      publishedToFinance: sourceInvoice.publishedToFinance || invoice.publishedToFinance || false,
      publishedAt: sourceInvoice.publishedAt?.toString() || invoice.publishedAt?.toString() || '',
      hsnCode: sourceInvoice.hsn?.code || invoice.hsn?.code || '',
      redberylAccount: sourceInvoice.redberylAccount?.name || invoice.redberylAccount?.name || '',
      notes: sourceInvoice.description || invoice.description || '',
      // Additional fields for enhanced invoice
      employeeName: sourceInvoice.candidate?.name || invoice.candidate?.name || (typeof invoice.candidate === 'string' ? invoice.candidate : '') || 'Prathamesh Kadam',
      employeeEngagementCode: sourceInvoice.candidate?.engagementCode || sourceInvoice.engagementCode || invoice.engagementCode || 'ENG-0018',
      joiningDate: sourceInvoice.candidate?.joiningDate || sourceInvoice.joiningDate || invoice.joiningDate || '',
      rate: sourceInvoice.project?.rate?.toString() || invoice.project?.rate?.toString() || sourceInvoice.rate?.toString() || invoice.rate?.toString() || '20000.00',
      billAmount: sourceInvoice.billingAmount?.toString() || invoice.billingAmount?.toString() || sourceInvoice.billAmount?.toString() || invoice.billAmount?.toString() || '8666.67',
      cgst: sourceInvoice.cgst?.toString() || (sourceInvoice.taxAmount ? (parseFloat(sourceInvoice.taxAmount.toString()) / 2).toString() : '780.00'),
      sgst: sourceInvoice.sgst?.toString() || (sourceInvoice.taxAmount ? (parseFloat(sourceInvoice.taxAmount.toString()) / 2).toString() : '780.00'),
      igst: sourceInvoice.igst?.toString() || '0.00',
      netPayable: sourceInvoice.totalAmount?.toString() || invoice.totalAmount?.toString() || sourceInvoice.netPayable?.toString() || invoice.netPayable?.toString() || '10226.67',
      bankName: sourceInvoice.redberylAccount?.bankName || sourceInvoice.bankName || invoice.bankName || 'HDFC Bank',
      branchName: sourceInvoice.redberylAccount?.branchName || sourceInvoice.branchName || invoice.branchName || 'MG Road Branch',
      accountName: sourceInvoice.redberylAccount?.accountName || sourceInvoice.redberylAccount?.name || sourceInvoice.accountName || invoice.accountName || 'Acme Corporation Pvt Ltd',
      accountNo: sourceInvoice.redberylAccount?.accountNo || sourceInvoice.accountNo || invoice.accountNo || '***********',
      ifscCode: sourceInvoice.redberylAccount?.ifscCode || sourceInvoice.ifscCode || invoice.ifscCode || 'HDFC0001234',
      accountType: sourceInvoice.redberylAccount?.accountType || sourceInvoice.accountType || invoice.accountType || 'Current',
      gstin: sourceInvoice.redberylAccount?.gstin || sourceInvoice.gstin || invoice.gstin || invoice.gstNumber || '29**********2Z5',
      cin: sourceInvoice.redberylAccount?.cin || sourceInvoice.cin || invoice.cin || 'U12345KA2020PTC123456',
      panNo: sourceInvoice.redberylAccount?.panNo || sourceInvoice.panNo || invoice.panNo || '**********',
      attendanceDays: sourceInvoice.attendanceDays || invoice.attendanceDays || 30
    };

    console.log('🔄 Transformed invoice data:', transformedInvoice);

    // Create a temporary div to render the invoice template
    const tempDiv = document.createElement('div');
    tempDiv.style.position = 'absolute';
    tempDiv.style.left = '-9999px';
    tempDiv.style.top = '-9999px';
    tempDiv.style.width = '800px';
    tempDiv.style.backgroundColor = 'white';
    document.body.appendChild(tempDiv);

    // Create a simpler HTML template instead of using React components
    const safeValue = (value: any, fallback: string = 'N/A') => {
      if (value === null || value === undefined || value === '') return fallback;
      return String(value);
    };

    const formatCurrency = (value: any) => {
      const num = parseFloat(String(value || '0'));
      return isNaN(num) ? '0' : num.toFixed(2);
    };

    const htmlContent = `
      <div style="width: 800px; padding: 30px; font-family: Arial, sans-serif; background: white; color: black; border: 1px solid #ddd;">
        <!-- Header with Logo -->
        <div style="margin-bottom: 40px; border-bottom: 1px solid #ddd; padding-bottom: 20px;">
          <div style="float: left;">
            <!-- RedBeryl Logo Box -->
            <div style="width: 140px; height: 50px; background: linear-gradient(135deg, #4285f4 0%, #ea4335 25%, #fbbc05 50%, #34a853 75%, #9c27b0 100%); border-radius: 6px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 18px; margin-bottom: 5px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
              RedBeryl
            </div>
            <p style="margin: 0; color: #666; font-size: 11px; font-weight: normal;">Tech Solutions Pvt. Ltd.</p>
          </div>
          <div style="clear: both;"></div>
        </div>

        <!-- Invoice Title -->
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="margin: 0; color: #333; font-size: 28px; text-decoration: underline; font-weight: bold;">INVOICE</h1>
        </div>

        <!-- Invoice Details and Billed To Section -->
        <div style="display: flex; justify-content: space-between; margin-bottom: 30px;">
          <div style="width: 48%;">
            <h3 style="margin: 0 0 15px 0; color: #333; font-size: 16px; font-weight: bold;">Invoice Details :-</h3>
            <div style="line-height: 1.6;">
              <p style="margin: 3px 0; font-size: 14px;"><strong>Invoice Date:</strong> ${safeValue(new Date(transformedInvoice.issueDate).toLocaleDateString('en-GB'))}</p>
              <p style="margin: 3px 0; font-size: 14px;"><strong>Invoice No.:</strong> ${safeValue(transformedInvoice.id)}</p>
              <p style="margin: 3px 0; font-size: 14px;"><strong>Invoice Month:</strong> ${new Date(transformedInvoice.issueDate).toLocaleDateString('en-US', { month: 'long', year: 'numeric' }).toUpperCase()}</p>
              <p style="margin: 3px 0; font-size: 14px;"><strong>Invoice For:</strong> Services</p>
              <p style="margin: 3px 0; font-size: 14px;"><strong>HSN No.:</strong> ${safeValue(transformedInvoice.hsnCode, '998313')}</p>
              <p style="margin: 3px 0; font-size: 14px;"><strong>Employee Name:</strong> ${safeValue(transformedInvoice.employeeName)}</p>
              <p style="margin: 3px 0; font-size: 14px;"><strong>Employee Engagement Code:</strong> ${safeValue(transformedInvoice.employeeEngagementCode, 'ENG-0018')}</p>
            </div>
          </div>
          <div style="width: 48%;">
            <h3 style="margin: 0 0 15px 0; color: #333; font-size: 16px; font-weight: bold;">Billed To :-</h3>
            <div style="line-height: 1.6;">
              <p style="margin: 3px 0; font-size: 14px; font-weight: bold;">${safeValue(transformedInvoice.client)}</p>
              <p style="margin: 3px 0; font-size: 14px;"><strong>GST No.:</strong> ${safeValue(transformedInvoice.gstin, '')}</p>
            </div>
          </div>
        </div>

        <!-- Main Table -->
        <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px; font-size: 12px; border: 2px solid #333;">
          <thead>
            <tr style="background-color: #f8f9fa;">
              <th style="border: 1px solid #333; padding: 12px; text-align: center; font-weight: bold;">Employee<br/>Name</th>
              <th style="border: 1px solid #333; padding: 12px; text-align: center; font-weight: bold;">Joining<br/>Date</th>
              <th style="border: 1px solid #333; padding: 12px; text-align: center; font-weight: bold;">Rate</th>
              <th style="border: 1px solid #333; padding: 12px; text-align: center; font-weight: bold;">Bill<br/>Amount</th>
              <th style="border: 1px solid #333; padding: 12px; text-align: center; font-weight: bold;">GST</th>
              <th style="border: 1px solid #333; padding: 12px; text-align: center; font-weight: bold;">Total Bill<br/>Amount</th>
            </tr>
            <tr style="background-color: #f8f9fa;">
              <th style="border: 1px solid #333; padding: 8px; text-align: center; font-size: 10px;"></th>
              <th style="border: 1px solid #333; padding: 8px; text-align: center; font-size: 10px;"></th>
              <th style="border: 1px solid #333; padding: 8px; text-align: center; font-size: 10px;"></th>
              <th style="border: 1px solid #333; padding: 8px; text-align: center; font-size: 10px;"></th>
              <th style="border: 1px solid #333; padding: 4px; text-align: center; font-size: 10px;">
                <div style="display: flex; justify-content: space-around;">
                  <span style="flex: 1; border-right: 1px solid #333; padding: 2px;">CGST<br/>@9%</span>
                  <span style="flex: 1; border-right: 1px solid #333; padding: 2px;">SGST<br/>@9%</span>
                  <span style="flex: 1; padding: 2px;">IGST<br/>@18%</span>
                </div>
              </th>
              <th style="border: 1px solid #333; padding: 8px; text-align: center; font-size: 10px;"></th>
            </tr>
          </thead>
          <tbody>
            <tr style="height: 60px;">
              <td style="border: 1px solid #333; padding: 12px; text-align: center; vertical-align: middle; font-size: 12px;">${safeValue(transformedInvoice.employeeName)}</td>
              <td style="border: 1px solid #333; padding: 12px; text-align: center; vertical-align: middle; font-size: 12px;">${safeValue(transformedInvoice.joiningDate, '')}</td>
              <td style="border: 1px solid #333; padding: 12px; text-align: center; vertical-align: middle; font-size: 12px;">₹${formatCurrency(transformedInvoice.rate)}</td>
              <td style="border: 1px solid #333; padding: 12px; text-align: center; vertical-align: middle; font-size: 12px;">₹${formatCurrency(transformedInvoice.billAmount)}</td>
              <td style="border: 1px solid #333; padding: 0; text-align: center; vertical-align: middle;">
                <table style="width: 100%; height: 100%; border-collapse: collapse;">
                  <tr>
                    <td style="border-right: 1px solid #333; padding: 8px; text-align: center; font-size: 11px; width: 33.33%;">₹${formatCurrency(transformedInvoice.cgst)}</td>
                    <td style="border-right: 1px solid #333; padding: 8px; text-align: center; font-size: 11px; width: 33.33%;">₹${formatCurrency(transformedInvoice.sgst)}</td>
                    <td style="padding: 8px; text-align: center; font-size: 11px; width: 33.33%;">₹${formatCurrency(transformedInvoice.igst)}</td>
                  </tr>
                </table>
              </td>
              <td style="border: 1px solid #333; padding: 12px; text-align: center; vertical-align: middle; font-weight: bold; font-size: 12px;">₹${formatCurrency(transformedInvoice.netPayable)}</td>
            </tr>
          </tbody>
        </table>

        <!-- GST Type -->
        <div style="margin-bottom: 20px; padding: 8px; background-color: #e3f2fd; border-left: 4px solid #2196f3; font-size: 14px;">
          <strong>GST Type:</strong> Intra-State (Maharashtra) - CGST (9%) + SGST (9%) = 18%
        </div>

        <!-- Net Payable -->
        <div style="margin-bottom: 30px; font-size: 14px; font-weight: bold;">
          <strong>Net Payable: ₹${formatCurrency(transformedInvoice.netPayable)} / (${convertNumberToWords(parseFloat(transformedInvoice.netPayable || '0'))} Rupees Only)</strong>
        </div>

        <!-- Payment Information and Authorized Signatory -->
        <table style="width: 100%; border-collapse: collapse; border: 2px solid #333;">
          <tr>
            <td style="border: 1px solid #333; padding: 15px; width: 50%; vertical-align: top;">
              <h3 style="margin: 0 0 10px 0; color: #333; font-size: 14px; font-weight: bold;">Payment Information</h3>
              <div style="font-size: 12px; line-height: 1.4;">
                <p style="margin: 2px 0;"><strong>Bank Name:</strong> ${safeValue(transformedInvoice.bankName)}</p>
                <p style="margin: 2px 0;"><strong>Branch Name:</strong> ${safeValue(transformedInvoice.branchName)}</p>
                <p style="margin: 2px 0;"><strong>Account Name:</strong> ${safeValue(transformedInvoice.accountName)}</p>
                <p style="margin: 2px 0;"><strong>Account No:</strong> ${safeValue(transformedInvoice.accountNo)}</p>
                <p style="margin: 2px 0;"><strong>IFSC Code:</strong> ${safeValue(transformedInvoice.ifscCode)}</p>
                <p style="margin: 2px 0;"><strong>Account Type:</strong> ${safeValue(transformedInvoice.accountType)}</p>
                <p style="margin: 2px 0;"><strong>GSTIN:</strong> ${safeValue(transformedInvoice.gstin)}</p>
                <p style="margin: 2px 0;"><strong>CIN:</strong> ${safeValue(transformedInvoice.cin)}</p>
                <p style="margin: 2px 0;"><strong>PAN No:</strong> ${safeValue(transformedInvoice.panNo)}</p>
              </div>
            </td>
            <td style="border: 1px solid #333; padding: 15px; width: 50%; vertical-align: top; text-align: center;">
              <h3 style="margin: 0 0 10px 0; color: #333; font-size: 14px; font-weight: bold;">Authorized Signatory</h3>
              <div style="margin-top: 80px;">
                <p style="font-size: 12px; font-weight: bold;">For RedBeryl Tech Solutions Pvt. Ltd.</p>
              </div>
            </td>
          </tr>
        </table>

        <!-- Thank you message -->
        <div style="text-align: center; margin-top: 20px; font-size: 12px; color: #666;">
          <p>Thank you for doing business with us.</p>
        </div>
      </div>
    `;

    tempDiv.innerHTML = htmlContent;

    // Wait for rendering to complete
    await new Promise(resolve => setTimeout(resolve, 1000));

    try {
      console.log('🔄 Capturing canvas...');
      console.log('🔄 TempDiv content preview:', tempDiv.innerHTML.substring(0, 200) + '...');

      // Use html2canvas to capture the rendered template with optimized settings
      const canvas = await html2canvas(tempDiv, {
        scale: 1.2, // Further reduced for smaller file size
        useCORS: true,
        logging: true, // Enable logging for debugging
        backgroundColor: '#ffffff',
        width: 800,
        allowTaint: true,
        imageTimeout: 15000,
        removeContainer: false,
        foreignObjectRendering: false
      });

      console.log('🔄 Canvas captured, size:', canvas.width, 'x', canvas.height);

      if (canvas.width === 0 || canvas.height === 0) {
        throw new Error('Canvas has zero dimensions - HTML rendering failed');
      }

      // Create PDF
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      });

      const imgWidth = 210; // A4 width in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      // Use JPEG with compression for smaller file size
      const imgData = canvas.toDataURL('image/jpeg', 0.7); // 70% quality for smaller size

      if (!imgData || imgData === 'data:,') {
        throw new Error('Failed to generate image data from canvas');
      }

      console.log('🔄 Adding image to PDF, dimensions:', imgWidth, 'x', imgHeight);
      pdf.addImage(imgData, 'JPEG', 0, 0, imgWidth, imgHeight);

      // Convert to blob
      const pdfBlob = pdf.output('blob');
      console.log('✅ PDF generated successfully, size:', pdfBlob.size, 'bytes');

      if (pdfBlob.size === 0) {
        throw new Error('Generated PDF is empty');
      }

      return pdfBlob;
    } finally {
      // Clean up
      document.body.removeChild(tempDiv);
    }
  } catch (error) {
    console.error('❌ Error generating PDF blob:', error);

    // Fallback: Create a simple text-based PDF
    try {
      console.log('🔄 Attempting fallback text-based PDF generation...');

      const formatCurrencyFallback = (value: any) => {
        const num = parseFloat(String(value || '0'));
        return isNaN(num) ? '0.00' : num.toFixed(2);
      };

      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      });

      // Add text content
      pdf.setFontSize(20);
      pdf.text('RedBeryl Tech Solutions Pvt. Ltd.', 105, 30, { align: 'center' });

      pdf.setFontSize(16);
      pdf.text('INVOICE', 105, 50, { align: 'center' });

      pdf.setFontSize(12);
      let yPos = 70;

      pdf.text(`Invoice No: ${transformedInvoice.id}`, 20, yPos);
      yPos += 10;
      pdf.text(`Invoice Date: ${new Date(transformedInvoice.issueDate).toLocaleDateString()}`, 20, yPos);
      yPos += 10;
      pdf.text(`Client: ${transformedInvoice.client}`, 20, yPos);
      yPos += 10;
      pdf.text(`Employee: ${transformedInvoice.employeeName}`, 20, yPos);
      yPos += 20;

      pdf.text(`Bill Amount: ₹${formatCurrencyFallback(transformedInvoice.billAmount)}`, 20, yPos);
      yPos += 10;
      pdf.text(`CGST @9%: ₹${formatCurrencyFallback(transformedInvoice.cgst)}`, 20, yPos);
      yPos += 10;
      pdf.text(`SGST @9%: ₹${formatCurrencyFallback(transformedInvoice.sgst)}`, 20, yPos);
      yPos += 10;
      pdf.text(`Total Amount: ₹${formatCurrencyFallback(transformedInvoice.netPayable)}`, 20, yPos);
      yPos += 20;

      pdf.text('Payment Information:', 20, yPos);
      yPos += 10;
      pdf.text(`Bank: ${transformedInvoice.bankName}`, 20, yPos);
      yPos += 10;
      pdf.text(`Account: ${transformedInvoice.accountNo}`, 20, yPos);
      yPos += 10;
      pdf.text(`IFSC: ${transformedInvoice.ifscCode}`, 20, yPos);

      const fallbackBlob = pdf.output('blob');
      console.log('✅ Fallback PDF generated successfully, size:', fallbackBlob.size, 'bytes');

      return fallbackBlob;
    } catch (fallbackError) {
      console.error('❌ Fallback PDF generation also failed:', fallbackError);
      throw new Error('Failed to generate PDF: ' + (error instanceof Error ? error.message : String(error)));
    }
  }
};

/**
 * Generate PDF base64 string from invoice data for OneDrive upload
 */
export const generateInvoicePdfBase64 = async (invoice: Invoice): Promise<string> => {
  try {
    const pdfBlob = await generateInvoicePdfBlob(invoice);
    
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // Remove the data URL prefix to get just the base64 string
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(pdfBlob);
    });
  } catch (error) {
    console.error('Error generating PDF base64:', error);
    throw new Error('Failed to generate PDF base64: ' + (error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Fetch PDF from backend API and convert to blob for OneDrive upload
 */
export const fetchInvoicePdfBlob = async (invoiceId: string | number): Promise<Blob> => {
  try {
    // Try multiple API endpoints
    const apiUrls = [
      `/api/invoice-generation/public/pdf/${invoiceId}`,
      `/api/invoice-generation/public/pdf/by-number/${invoiceId}`,
      `/api/invoice-generation/pdf/${invoiceId}`
    ];

    let lastError = null;

    for (const url of apiUrls) {
      try {
        console.log(`Fetching PDF from: ${url}`);
        
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Accept': 'application/pdf',
          }
        });

        if (response.ok) {
          const blob = await response.blob();
          if (blob.size > 0) {
            console.log(`Successfully fetched PDF from: ${url}`);
            return blob;
          }
        }
      } catch (error) {
        console.warn(`Failed to fetch from ${url}:`, error);
        lastError = error;
      }
    }

    throw new Error(`Failed to fetch PDF from all endpoints. Last error: ${lastError}`);
  } catch (error) {
    console.error('Error fetching PDF blob:', error);
    throw new Error('Failed to fetch PDF: ' + (error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Fetch PDF from backend API and convert to base64 for OneDrive upload
 */
export const fetchInvoicePdfBase64 = async (invoiceId: string | number): Promise<string> => {
  try {
    const pdfBlob = await fetchInvoicePdfBlob(invoiceId);
    
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // Remove the data URL prefix to get just the base64 string
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(pdfBlob);
    });
  } catch (error) {
    console.error('Error fetching PDF base64:', error);
    throw new Error('Failed to fetch PDF base64: ' + (error instanceof Error ? error.message : String(error)));
  }
};

/**
 * Download PDF blob as file
 */
export const downloadPdfBlob = (blob: Blob, filename: string) => {
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};


