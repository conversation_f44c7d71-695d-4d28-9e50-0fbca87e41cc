@echo off
echo Switching to localhost configuration...

cd /d "%~dp0\..\frontend"

echo # Backend URL Configuration > .env
echo # Change this to match your backend server IP address >> .env
echo VITE_BACKEND_URL=http://localhost:8091 >> .env
echo VITE_API_URL=http://localhost:8091/api >> .env
echo. >> .env
echo # For IP development, use: >> .env
echo # VITE_BACKEND_URL=http://*************:8091 >> .env
echo # VITE_API_URL=http://*************:8091/api >> .env

echo Configuration switched to localhost
echo Please restart the frontend server for changes to take effect.
pause
