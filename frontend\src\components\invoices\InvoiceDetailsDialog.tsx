import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { Printer, ReceiptIcon, Loader2 } from "lucide-react";
import { toast } from "sonner";

// Flag to track if we're in a browser environment
const isBrowser = typeof window !== 'undefined';

interface InvoiceDetailsProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  invoice: {
    id: string | number;
    databaseId?: number;
    client: string;
    project: string;
    candidate?: string;
    invoiceType?: string;
    staffingType?: string;
    amount: string;
    tax: string;
    total: string;
    issueDate: string;
    dueDate: string;
    status: string;
    recurring: boolean;
    publishedToFinance?: boolean;
    publishedAt?: string;
    hsnCode?: string;
    redberylAccount?: string;
    notes?: string;
    originalInvoice?: any;
    [key: string]: any; // Allow for additional properties
  } | null;
  onEdit: (id: string) => void;
}

const InvoiceDetailsDialog: React.FC<InvoiceDetailsProps> = ({
  open,
  onOpenChange,
  invoice,
}) => {

  // If no invoice is provided, return null
  if (!invoice) {
    console.log("No invoice data provided to InvoiceDetailsDialog");
    return null;
  }

  // Log the invoice data for debugging
  console.log("InvoiceDetailsDialog: Invoice data:", invoice);

  // Ensure all required fields are present
  const safeInvoice = {
    ...invoice,
    client: invoice.client || "Unknown Client",
    project: invoice.project || "Unknown Project",
    candidate: invoice.candidate || "-",
    invoiceType: invoice.invoiceType || "Standard",
    staffingType: invoice.staffingType || "Full-time",
    amount: invoice.amount || "₹0.00",
    tax: invoice.tax || "₹0.00",
    total: invoice.total || "₹0.00",
    issueDate: invoice.issueDate || new Date().toISOString().split('T')[0],
    dueDate: invoice.dueDate || new Date().toISOString().split('T')[0],
    status: invoice.status || "Draft",
    recurring: invoice.recurring || false,
    notes: invoice.notes || ""
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "paid":
        return "bg-green-100 text-green-800 border-green-200";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "overdue":
        return "bg-red-100 text-red-800 border-red-200";
      case "draft":
        return "bg-gray-100 text-gray-800 border-gray-200";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };



  const [isPrinting, setIsPrinting] = useState(false);

  const handlePrint = async () => {
    setIsPrinting(true);

    try {
      // Make sure we're in a browser environment
      if (!isBrowser) {
        console.error('Cannot print in non-browser environment');
        toast.error("Printing is only available in browser environments");
        setIsPrinting(false);
        return;
      }

      // Create a new window for printing
      const printWindow = window.open('', '_blank', 'width=800,height=600');
      if (!printWindow) {
        throw new Error('Pop-up blocked. Please allow pop-ups for this site to enable printing.');
      }

      // Write the HTML structure to the new window
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>Print Invoice ${safeInvoice.id}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 0;
              padding: 20px;
              color: #000;
              background-color: #fff;
              font-size: 12px;
              line-height: 1.4;
            }

            .invoice-container {
              max-width: 800px;
              margin: 0 auto;
              padding: 20px;
              background-color: white;
              position: relative;
            }

            .invoice-header {
              text-align: center;
              font-size: 24px;
              font-weight: bold;
              margin: 40px 0 30px 0;
              text-decoration: underline;
            }

            .invoice-details-section {
              display: flex;
              justify-content: space-between;
              margin-bottom: 30px;
              font-size: 12px;
            }

            .invoice-details-left, .invoice-details-right {
              width: 48%;
            }

            .invoice-details-title {
              font-weight: bold;
              margin-bottom: 10px;
              font-size: 13px;
            }

            .invoice-details-row {
              margin-bottom: 3px;
              line-height: 1.3;
            }

            .invoice-table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 20px;
              font-size: 11px;
            }

            .invoice-table th, .invoice-table td {
              border: 1px solid #000;
              padding: 6px 4px;
              text-align: center;
              vertical-align: middle;
            }

            .invoice-table th {
              background-color: #f8f9fa;
              font-weight: bold;
              font-size: 10px;
            }

            .invoice-table td {
              font-size: 11px;
            }

            .net-payable {
              font-weight: bold;
              margin: 15px 0;
              font-size: 13px;
            }

            .payment-info-section {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 20px;
              font-size: 11px;
            }

            .payment-info-header {
              background-color: #f8f9fa;
              font-weight: bold;
              padding: 8px;
              border: 1px solid #000;
              text-align: center;
              font-size: 12px;
            }

            .payment-info {
              width: 60%;
              vertical-align: top;
              padding: 8px;
            }

            .authorized-signatory {
              width: 40%;
              text-align: center;
              vertical-align: middle;
              padding: 8px;
            }

            .signatory-text {
              font-weight: bold;
              font-size: 12px;
            }

            .payment-info-row {
              display: flex;
              margin-bottom: 3px;
              align-items: flex-start;
            }

            .payment-info-label-text {
              font-weight: bold;
              width: 100px;
              flex-shrink: 0;
              font-size: 11px;
            }

            .payment-info-value {
              flex: 1;
              font-size: 11px;
            }

            .thank-you {
              text-align: center;
              margin-top: 20px;
              font-style: italic;
              font-size: 12px;
            }

            @media print {
              body {
                margin: 0;
                padding: 10px;
                font-size: 11px;
              }
              .invoice-container {
                border: none;
                padding: 10px;
              }
              .invoice-header {
                margin: 20px 0 20px 0;
                font-size: 22px;
              }
              .no-print {
                display: none;
              }
            }

            .print-header {
              text-align: center;
              margin-bottom: 20px;
              padding: 10px;
              background: #f5f5f5;
              border-radius: 5px;
            }

            .print-button {
              background: #007bff;
              color: white;
              border: none;
              padding: 10px 20px;
              border-radius: 5px;
              cursor: pointer;
              margin-right: 10px;
            }

            .print-button:hover {
              background: #0056b3;
            }

            .close-button {
              background: #6c757d;
              color: white;
              border: none;
              padding: 10px 20px;
              border-radius: 5px;
              cursor: pointer;
            }

            .close-button:hover {
              background: #545b62;
            }
          </style>
        </head>
        <body>
          <div class="print-header no-print">
            <h2>Invoice ${safeInvoice.id} - Print Preview</h2>
            <button class="print-button" onclick="window.print()">🖨️ Print</button>
            <button class="close-button" onclick="window.close()">✕ Close</button>
          </div>
          <div id="invoice-content"></div>
        </body>
        </html>
      `);

      // Close the document to finish loading
      printWindow.document.close();

      // Wait for the window to load
      await new Promise(resolve => {
        printWindow.onload = resolve;
        // Fallback timeout
        setTimeout(resolve, 1000);
      });

      // Create the invoice content HTML using the exact same format as backend template
      const invoiceHTML = `
        <div class="invoice-container">
          <!-- Logo positioned at top right -->
          <div style="position: absolute; top: 20px; right: 20px; width: 200px; height: 100px;">
            <svg width="200" height="100" viewBox="0 0 200 100" style="background: white;">
              <text x="10" y="30" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#d32f2f">RedBeryl</text>
              <text x="10" y="50" font-family="Arial, sans-serif" font-size="12" fill="#666666">Tech Solutions Pvt. Ltd.</text>
              <text x="10" y="70" font-family="Arial, sans-serif" font-size="10" fill="#666666" font-style="italic">Integrates Business With Technology</text>
            </svg>
          </div>

          <div class="invoice-header">INVOICE</div>

          <div class="invoice-details-section">
            <div class="invoice-details-left">
              <div class="invoice-details-title">Invoice Details :-</div>
              <div class="invoice-details-row">Invoice Date: ${new Date(safeInvoice.issueDate).toLocaleDateString('en-GB')}</div>
              <div class="invoice-details-row">Invoice No.: ${safeInvoice.id}</div>
              <div class="invoice-details-row">Invoice Month: ${new Date(safeInvoice.issueDate).toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}</div>
              <div class="invoice-details-row">Invoice For: Services</div>
              <div class="invoice-details-row">HSN No.: 998313</div>
              <div class="invoice-details-row">Employee Name: ${safeInvoice.candidate || 'N/A'}</div>
              <div class="invoice-details-row">Employee Engagement Code: ENG-0018</div>
            </div>

            <div class="invoice-details-right">
              <div class="invoice-details-title">Billed To :-</div>
              <div class="invoice-details-row">${safeInvoice.client}</div>
              <div class="invoice-details-row">GST No:</div>
            </div>
          </div>

          <table class="invoice-table">
            <thead>
              <tr>
                <th rowspan="2">Employee Name</th>
                <th rowspan="2">Joining Date</th>
                <th rowspan="2">Rate</th>
                <th rowspan="2">Bill Amount</th>
                <th colspan="3">GST</th>
                <th rowspan="2">Total Bill Amount</th>
              </tr>
              <tr>
                <th>CGST @9%</th>
                <th>SGST @9%</th>
                <th>IGST @18%</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td rowspan="2">${safeInvoice.candidate || 'N/A'}</td>
                <td rowspan="2"></td>
                <td>₹20,000.00</td>
                <td>₹8,666.67</td>
                <td>₹780.00</td>
                <td>₹780.00</td>
                <td></td>
                <td>${safeInvoice.total}</td>
              </tr>
            </tbody>
          </table>

          <div style="margin: 10px 0; padding: 8px; background-color: #f8f9fa; border-left: 4px solid #007bff; font-size: 12px;">
            <strong>GST Type:</strong>
            <span style="color: #28a745;">Intra-State (Maharashtra) - CGST (9%) + SGST (9%) = 18%</span>
          </div>

          <div class="net-payable">
            Net Payable: ${safeInvoice.total} /- (Ten Thousand Two Hundred Twenty Six and Sixty Seven Paisa)
          </div>

          <table class="payment-info-section" border="1" cellspacing="0" cellpadding="8">
            <tr>
              <th class="payment-info-header">Payment Information</th>
              <th class="payment-info-header">Authorized Signatory</th>
            </tr>
            <tr>
              <td class="payment-info">
                <div class="payment-info-row">
                  <div class="payment-info-label-text">Bank Name:</div>
                  <div class="payment-info-value">HDFC Bank</div>
                </div>
                <div class="payment-info-row">
                  <div class="payment-info-label-text">Branch Name:</div>
                  <div class="payment-info-value">MG Road Branch</div>
                </div>
                <div class="payment-info-row">
                  <div class="payment-info-label-text">Account Name:</div>
                  <div class="payment-info-value">Acme Corporation Pvt Ltd</div>
                </div>
                <div class="payment-info-row">
                  <div class="payment-info-label-text">Account No:</div>
                  <div class="payment-info-value">************</div>
                </div>
                <div class="payment-info-row">
                  <div class="payment-info-label-text">IFSC Code:</div>
                  <div class="payment-info-value">HDFC0001234</div>
                </div>
                <div class="payment-info-row">
                  <div class="payment-info-label-text">Account Type:</div>
                  <div class="payment-info-value">Current</div>
                </div>
                <div class="payment-info-row">
                  <div class="payment-info-label-text">GSTN:</div>
                  <div class="payment-info-value">29**********2Z5</div>
                </div>
                <div class="payment-info-row">
                  <div class="payment-info-label-text">CIN:</div>
                  <div class="payment-info-value">U12345KA2020PTC012345</div>
                </div>
                <div class="payment-info-row">
                  <div class="payment-info-label-text">PAN No:</div>
                  <div class="payment-info-value">**********</div>
                </div>
              </td>
              <td class="authorized-signatory">
                <div class="signatory-text">For RedBeryl Tech Solutions Pvt. Ltd.</div>
              </td>
            </tr>
          </table>

          <div class="thank-you">
            Thank you for doing business with us.
          </div>
        </div>
      `;

      // Insert the invoice content
      printWindow.document.getElementById('invoice-content').innerHTML = invoiceHTML;

      toast.success("Invoice opened for printing");
    } catch (error) {
      console.error("Error preparing invoice for print:", error);
      toast.error("Failed to open invoice for printing");

      // Show more detailed error message
      if (error instanceof Error) {
        console.error("Print error details:", error.message);
        toast.error(`Print error: ${error.message}`);
      }
    } finally {
      setIsPrinting(false);
    }
  };



  return (
    <Dialog open={open} onOpenChange={onOpenChange} modal={true}>
      <DialogContent className="sm:max-w-[600px] bg-white">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>Invoice {safeInvoice.id}</span>
            <div className="flex items-center gap-2">
              {safeInvoice.recurring && (
                <Badge variant="outline" className="bg-blue-50 border-blue-200 text-blue-700">
                  Recurring
                </Badge>
              )}
              <Badge variant="outline" className={getStatusColor(safeInvoice.status)}>
                {safeInvoice.status}
              </Badge>
            </div>
          </DialogTitle>
          <DialogDescription>
            View invoice details and perform actions
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-2 gap-4 py-4">
          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">Client</p>
            <p className="text-sm font-semibold">{safeInvoice.client}</p>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">Project</p>
            <p className="text-sm font-semibold">{safeInvoice.project}</p>
          </div>

          {safeInvoice.candidate && safeInvoice.candidate !== "-" && (
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Candidate</p>
              <p className="text-sm font-semibold">{safeInvoice.candidate}</p>
            </div>
          )}

          {safeInvoice.invoiceType && (
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Invoice Type</p>
              <p className="text-sm">{safeInvoice.invoiceType}</p>
            </div>
          )}

          {safeInvoice.staffingType && (
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Staffing Type</p>
              <p className="text-sm">{safeInvoice.staffingType}</p>
            </div>
          )}

          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">Issue Date</p>
            <p className="text-sm">{format(new Date(safeInvoice.issueDate), "MMMM d, yyyy")}</p>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">Due Date</p>
            <p className="text-sm">{format(new Date(safeInvoice.dueDate), "MMMM d, yyyy")}</p>
          </div>

          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">Billing Amount</p>
            <p className="text-sm">{safeInvoice.amount}</p>
          </div>
          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">Tax Amount</p>
            <p className="text-sm">{safeInvoice.tax}</p>
          </div>
          <div className="space-y-1 col-span-2">
            <p className="text-sm font-medium text-muted-foreground">Total Amount</p>
            <p className="text-lg font-bold">{safeInvoice.total}</p>
          </div>

          {safeInvoice.hsnCode && (
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">HSN Code</p>
              <p className="text-sm">{safeInvoice.hsnCode}</p>
            </div>
          )}

          {safeInvoice.redberylAccount && (
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Redberyl Account</p>
              <p className="text-sm">{safeInvoice.redberylAccount}</p>
            </div>
          )}

          <div className="space-y-1">
            <p className="text-sm font-medium text-muted-foreground">Recurring</p>
            <p className="text-sm">{safeInvoice.recurring ? "Yes" : "No"}</p>
          </div>

          {typeof safeInvoice.publishedToFinance !== 'undefined' && (
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Published to Finance</p>
              <p className="text-sm">{safeInvoice.publishedToFinance ? "Yes" : "No"}</p>
            </div>
          )}

          {safeInvoice.publishedAt && (
            <div className="space-y-1">
              <p className="text-sm font-medium text-muted-foreground">Published At</p>
              <p className="text-sm">{format(new Date(safeInvoice.publishedAt), "MMMM d, yyyy HH:mm")}</p>
            </div>
          )}

          {safeInvoice.notes && (
            <div className="space-y-1 col-span-2">
              <p className="text-sm font-medium text-muted-foreground">Notes</p>
              <p className="text-sm">{safeInvoice.notes}</p>
            </div>
          )}
        </div>

        <DialogFooter className="flex flex-col gap-3 sm:flex-row sm:justify-between">
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              size="sm"
              className="gap-1"
              onClick={handlePrint}
              disabled={isPrinting}
            >
              {isPrinting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Printing...</span>
                </>
              ) : (
                <>
                  <Printer className="h-4 w-4" />
                  <span>Print</span>
                </>
              )}
            </Button>
            {safeInvoice.status.toLowerCase() !== "paid" && (
              <Button variant="outline" size="sm" className="gap-1" onClick={() => {
                toast.info("Payment recording functionality will be implemented soon");
              }}>
                <ReceiptIcon className="h-4 w-4" />
                <span>Record Payment</span>
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default InvoiceDetailsDialog;