package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.InvoiceDto;
import com.redberyl.invoiceapp.entity.Invoice;
import com.redberyl.invoiceapp.entity.RedberylAccount;
import com.redberyl.invoiceapp.entity.Client;
import com.redberyl.invoiceapp.entity.Project;
import com.redberyl.invoiceapp.entity.Candidate;
import com.redberyl.invoiceapp.entity.HsnCode;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.repository.InvoiceRepository;
import com.redberyl.invoiceapp.repository.RedberylAccountRepository;
import com.redberyl.invoiceapp.repository.ClientRepository;
import com.redberyl.invoiceapp.repository.ProjectRepository;
import com.redberyl.invoiceapp.repository.CandidateRepository;
import com.redberyl.invoiceapp.repository.HsnCodeRepository;
import com.redberyl.invoiceapp.service.InvoiceGenerationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * Controller for generating invoices in various formats
 */
@RestController
@RequestMapping("/api/invoice-generation")
@CrossOrigin(origins = {"http://localhost:3060", "http://127.0.0.1:3060"})
@Tag(name = "Invoice Generation", description = "API for generating invoices in various formats")
public class InvoiceGenerationController {

    @Autowired
    private InvoiceGenerationService invoiceGenerationService;

    @Autowired
    private InvoiceRepository invoiceRepository;

    @Autowired
    private RedberylAccountRepository redberylAccountRepository;

    @Autowired
    private ClientRepository clientRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private CandidateRepository candidateRepository;

    @Autowired
    private HsnCodeRepository hsnCodeRepository;

    /**
     * Generate a PDF invoice from an existing invoice
     *
     * @param invoiceId The ID of the invoice to generate
     * @return The PDF invoice as a downloadable resource
     */
    @GetMapping("/pdf/{invoiceId}")
    @Operation(summary = "Generate PDF invoice", description = "Generate a PDF invoice from an existing invoice")
    public ResponseEntity<Resource> generatePdfInvoice(@PathVariable Long invoiceId) {
        try {
            System.out.println("=== PDF Generation Request ===");
            System.out.println("Requested Invoice ID: " + invoiceId);

            Invoice invoice = invoiceRepository.findById(invoiceId)
                    .orElseThrow(() -> new ResourceNotFoundException("Invoice not found with id: " + invoiceId));

            // Log invoice details for debugging
            System.out.println("Invoice Number: " + invoice.getInvoiceNumber());
            System.out.println("Client: " + (invoice.getClient() != null ? invoice.getClient().getName() : "NULL"));
            System.out.println("Project: " + (invoice.getProject() != null ? invoice.getProject().getName() : "NULL"));
            System.out.println("Candidate: " + (invoice.getCandidate() != null ? invoice.getCandidate().getName() : "NULL"));
            System.out.println("Billing Amount: " + invoice.getBillingAmount());
            System.out.println("Total Amount: " + invoice.getTotalAmount());

            // Validate critical fields
            if (invoice.getInvoiceNumber() == null || invoice.getInvoiceNumber().trim().isEmpty()) {
                throw new IllegalStateException("Invoice number is missing or empty");
            }
            if (invoice.getBillingAmount() == null) {
                throw new IllegalStateException("Billing amount is missing");
            }

            System.out.println("Found invoice: " + invoice.getInvoiceNumber());
            Resource pdfResource = invoiceGenerationService.generatePdfInvoice(invoice);
            try {
                System.out.println("PDF generated successfully, size: " + pdfResource.contentLength() + " bytes");
            } catch (Exception e) {
                System.out.println("PDF generated successfully (size check failed): " + e.getMessage());
            }

            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"invoice-" + invoice.getInvoiceNumber().replaceAll("[/\\\\]", "-") + ".pdf\"")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "GET, POST, PUT, DELETE, OPTIONS")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "*")
                    .body(pdfResource);
        } catch (Exception e) {
            System.err.println("Error generating PDF: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * Generate a PDF invoice from invoice data without saving it
     *
     * @param invoiceDto The invoice data to use for generation
     * @return The PDF invoice as a downloadable resource
     */
    @PostMapping("/pdf/preview")
    @Operation(summary = "Generate PDF invoice preview", description = "Generate a PDF invoice from invoice data without saving it")
    public ResponseEntity<Resource> generatePdfInvoicePreview(@RequestBody InvoiceDto invoiceDto) {
        try {
            System.out.println("=== PDF PREVIEW REQUEST ===");
            System.out.println("Received invoice data for PDF preview");

            // Log important fields for debugging
            System.out.println("Invoice Number: " + invoiceDto.getInvoiceNumber());
            System.out.println("Client ID: " + invoiceDto.getClientId());
            System.out.println("Project ID: " + invoiceDto.getProjectId());
            System.out.println("Invoice Type ID: " + invoiceDto.getInvoiceTypeId());
            System.out.println("Billing Amount: " + invoiceDto.getBillingAmount());
            System.out.println("Tax Amount: " + invoiceDto.getTaxAmount());
            System.out.println("Total Amount: " + invoiceDto.getTotalAmount());

            Resource pdfResource = invoiceGenerationService.generatePdfInvoiceFromDto(invoiceDto);
            System.out.println("✓ PDF resource generated successfully");

            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"invoice-preview.pdf\"")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "GET, POST, PUT, DELETE, OPTIONS")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "*")
                    .body(pdfResource);
        } catch (Exception e) {
            System.err.println("❌ Error generating PDF preview: " + e.getMessage());
            e.printStackTrace();

            // Return a more detailed error response
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*")
                    .header(HttpHeaders.CONTENT_TYPE, "application/json")
                    .body(null);
        }
    }

    /**
     * Generate an HTML invoice from an existing invoice
     *
     * @param invoiceId The ID of the invoice to generate
     * @return The HTML invoice as a string
     */
    @GetMapping("/html/{invoiceId}")
    @Operation(summary = "Generate HTML invoice", description = "Generate an HTML invoice from an existing invoice")
    public ResponseEntity<String> generateHtmlInvoice(@PathVariable Long invoiceId) {
        Invoice invoice = invoiceRepository.findById(invoiceId)
                .orElseThrow(() -> new ResourceNotFoundException("Invoice not found with id: " + invoiceId));

        String htmlContent = invoiceGenerationService.generateHtmlInvoice(invoice);

        return ResponseEntity.ok()
                .contentType(MediaType.TEXT_HTML)
                .body(htmlContent);
    }

    /**
     * Generate an HTML invoice from invoice data without saving it
     *
     * @param invoiceDto The invoice data to use for generation
     * @return The HTML invoice as a string
     */
    @PostMapping("/html/preview")
    @Operation(summary = "Generate HTML invoice preview", description = "Generate an HTML invoice from invoice data without saving it")
    public ResponseEntity<String> generateHtmlInvoicePreview(@RequestBody InvoiceDto invoiceDto) {
        try {
            String htmlContent = invoiceGenerationService.generateHtmlInvoiceFromDto(invoiceDto);

            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_HTML)
                    .body(htmlContent);
        } catch (Exception e) {
            e.printStackTrace();
            throw new CustomException("Failed to generate HTML invoice preview: " + e.getMessage(), e);
        }
    }

    /**
     * Public endpoint for generating a PDF invoice from an existing invoice
     *
     * @param invoiceId The ID of the invoice to generate
     * @return The PDF invoice as a downloadable resource
     */
    @GetMapping("/public/pdf/{invoiceId}")
    @Operation(summary = "Generate PDF invoice (public)", description = "Public endpoint for generating a PDF invoice from an existing invoice")
    public ResponseEntity<Resource> generatePublicPdfInvoice(@PathVariable Long invoiceId) {
        System.out.println("=== PDF Generation Request ===");
        System.out.println("Requested Invoice ID: " + invoiceId);

        // Use the new method that eagerly loads all related entities
        Invoice invoice = invoiceRepository.findByIdWithAllRelations(invoiceId)
                .orElseThrow(() -> {
                    System.out.println("Invoice not found with ID: " + invoiceId);
                    return new ResourceNotFoundException("Invoice not found with id: " + invoiceId);
                });

        System.out.println("Found Invoice: " + invoice.getInvoiceNumber());
        System.out.println("Invoice Client: " + (invoice.getClient() != null ? invoice.getClient().getName() : "NULL"));
        System.out.println("Invoice Project: " + (invoice.getProject() != null ? invoice.getProject().getName() : "NULL"));
        System.out.println("Invoice Candidate: " + (invoice.getCandidate() != null ? invoice.getCandidate().getName() : "NULL"));
        System.out.println("Invoice HSN Code: " + (invoice.getHsnCode() != null ? invoice.getHsnCode().getCode() : "NULL"));
        System.out.println("Invoice Redberyl Account: " + (invoice.getRedberylAccount() != null ? invoice.getRedberylAccount().getAccountName() : "NULL"));
        System.out.println("Invoice Billing Amount: " + invoice.getBillingAmount());
        System.out.println("Invoice Total Amount: " + invoice.getTotalAmount());

        // Validate critical fields
        if (invoice.getInvoiceNumber() == null || invoice.getInvoiceNumber().trim().isEmpty()) {
            throw new IllegalStateException("Invoice number is missing or empty");
        }
        if (invoice.getBillingAmount() == null) {
            throw new IllegalStateException("Billing amount is missing");
        }

        Resource pdfResource = invoiceGenerationService.generatePdfInvoice(invoice);
        try {
            System.out.println("PDF generated successfully, size: " + pdfResource.contentLength() + " bytes");
        } catch (Exception e) {
            System.out.println("PDF generated successfully (size check failed): " + e.getMessage());
        }

        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_PDF)
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"invoice-" + invoice.getInvoiceNumber().replaceAll("[/\\\\]", "-") + ".pdf\"")
                .header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*")
                .header(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "GET, POST, PUT, DELETE, OPTIONS")
                .header(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "*")
                .body(pdfResource);
    }

    /**
     * Simple test endpoint to verify the API is working
     *
     * @return A simple text message
     */
    @GetMapping("/test")
    @Operation(summary = "Test API", description = "Simple endpoint to test if the API is working")
    public ResponseEntity<String> testApi() {
        System.out.println("=== TEST API CALLED ===");
        return ResponseEntity.ok()
                .header("Access-Control-Allow-Origin", "*")
                .header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
                .header("Access-Control-Allow-Headers", "*")
                .body("Invoice Generation API is working!");
    }

    /**
     * Simple test endpoint to verify the PDF preview API is working
     *
     * @return A simple text message
     */
    @PostMapping("/pdf/preview/test")
    @Operation(summary = "Test PDF Preview API", description = "Simple endpoint to test if the PDF preview API is working")
    public ResponseEntity<String> testPdfPreviewApi(@RequestBody(required = false) Object requestBody) {
        System.out.println("Test PDF Preview API called with body: " + requestBody);
        return ResponseEntity.ok()
                .header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*")
                .header(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "GET, POST, PUT, DELETE, OPTIONS")
                .header(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "*")
                .body("PDF Preview API endpoint is working! Received: " + (requestBody != null ? requestBody.toString() : "null"));
    }

    /**
     * Generate a minimal PDF for testing
     */
    @PostMapping("/pdf/preview/minimal")
    @Operation(summary = "Generate minimal PDF", description = "Generate a minimal PDF for testing")
    public ResponseEntity<Resource> generateMinimalPdf(@RequestBody(required = false) Object requestBody) {
        try {
            System.out.println("=== MINIMAL PDF TEST ===");
            System.out.println("Request body: " + requestBody);

            // Create a minimal invoice DTO for testing
            InvoiceDto testDto = new InvoiceDto();
            testDto.setInvoiceNumber("TEST-MINIMAL");
            testDto.setBillingAmount(new BigDecimal("10000"));
            testDto.setTaxAmount(new BigDecimal("1800"));
            testDto.setTotalAmount(new BigDecimal("11800"));

            Resource pdfResource = invoiceGenerationService.generatePdfInvoiceFromDto(testDto);
            System.out.println("✓ Minimal PDF generated successfully");

            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"minimal-test.pdf\"")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "GET, POST, PUT, DELETE, OPTIONS")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "*")
                    .body(pdfResource);
        } catch (Exception e) {
            System.err.println("❌ Error generating minimal PDF: " + e.getMessage());
            e.printStackTrace();

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*")
                    .header(HttpHeaders.CONTENT_TYPE, "application/json")
                    .body(null);
        }
    }

    /**
     * Test endpoint to generate a sample invoice PDF
     *
     * @return A sample PDF invoice
     */
    @GetMapping("/test/sample-pdf")
    @Operation(summary = "Generate sample PDF", description = "Generate a sample PDF invoice for testing")
    public ResponseEntity<Resource> generateSamplePdf() {
        try {
            // Find the first invoice in the database
            List<Invoice> invoices = invoiceRepository.findAll();
            if (invoices.isEmpty()) {
                return ResponseEntity.notFound().build();
            }

            Invoice invoice = invoices.get(0);
            Resource pdfResource = invoiceGenerationService.generatePdfInvoice(invoice);

            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"sample-invoice.pdf\"")
                    .body(pdfResource);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Check if an invoice exists
     *
     * @param invoiceId The ID of the invoice to check
     * @return Invoice details if exists
     */
    @GetMapping("/check/{invoiceId}")
    @Operation(summary = "Check invoice existence", description = "Check if an invoice exists in the database")
    public ResponseEntity<String> checkInvoice(@PathVariable Long invoiceId) {
        try {
            Invoice invoice = invoiceRepository.findById(invoiceId).orElse(null);
            if (invoice == null) {
                return ResponseEntity.notFound().build();
            }

            return ResponseEntity.ok("Invoice found: " + invoice.getInvoiceNumber() +
                                   ", Client: " + (invoice.getClient() != null ? invoice.getClient().getName() : "N/A") +
                                   ", Total: " + invoice.getTotalAmount());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error checking invoice: " + e.getMessage());
        }
    }

    /**
     * List all invoices with their IDs for debugging
     *
     * @return List of all invoices with their database IDs
     */
    @GetMapping("/list-all")
    @Operation(summary = "List all invoices", description = "List all invoices with their database IDs for debugging")
    public ResponseEntity<String> listAllInvoices() {
        try {
            List<Invoice> invoices = invoiceRepository.findAll();
            if (invoices.isEmpty()) {
                return ResponseEntity.ok("No invoices found in database");
            }

            StringBuilder result = new StringBuilder("All invoices in database:\n");
            for (Invoice invoice : invoices) {
                result.append("Database ID: ").append(invoice.getId())
                      .append(", Invoice Number: ").append(invoice.getInvoiceNumber())
                      .append(", Client: ").append(invoice.getClient() != null ? invoice.getClient().getName() : "N/A")
                      .append(", Total: ").append(invoice.getTotalAmount())
                      .append("\n");
            }

            return ResponseEntity.ok(result.toString());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error listing invoices: " + e.getMessage());
        }
    }

    /**
     * Debug endpoint to check invoice data
     *
     * @param invoiceId The ID of the invoice to check
     * @return JSON with invoice details
     */
    @GetMapping("/debug/{invoiceId}")
    @Operation(summary = "Debug invoice data", description = "Check invoice data for debugging PDF generation issues")
    public ResponseEntity<Map<String, Object>> debugInvoice(@PathVariable String invoiceId) {
        try {
            System.out.println("=== Debug Invoice Request ===");
            System.out.println("Requested Invoice ID: " + invoiceId);

            Invoice invoice;
            try {
                // Try to parse as Long first (for numeric IDs)
                Long id = Long.parseLong(invoiceId);
                invoice = invoiceRepository.findById(id)
                        .orElseThrow(() -> new ResourceNotFoundException("Invoice not found with id: " + invoiceId));
            } catch (NumberFormatException e) {
                // If not numeric, try as invoice number (URL decode first)
                String decodedInvoiceNumber = URLDecoder.decode(invoiceId, StandardCharsets.UTF_8);
                invoice = invoiceRepository.findByInvoiceNumber(decodedInvoiceNumber)
                        .orElseThrow(() -> new ResourceNotFoundException("Invoice not found with number: " + decodedInvoiceNumber));
            }

            Map<String, Object> debugInfo = new HashMap<>();
            debugInfo.put("id", invoice.getId());
            debugInfo.put("invoiceNumber", invoice.getInvoiceNumber());
            debugInfo.put("billingAmount", invoice.getBillingAmount());
            debugInfo.put("totalAmount", invoice.getTotalAmount());
            debugInfo.put("hasClient", invoice.getClient() != null);
            debugInfo.put("hasProject", invoice.getProject() != null);
            debugInfo.put("hasCandidate", invoice.getCandidate() != null);
            debugInfo.put("hasHsnCode", invoice.getHsnCode() != null);
            debugInfo.put("hasRedberylAccount", invoice.getRedberylAccount() != null);

            if (invoice.getClient() != null) {
                debugInfo.put("clientName", invoice.getClient().getName());
            }
            if (invoice.getProject() != null) {
                debugInfo.put("projectName", invoice.getProject().getName());
            }
            if (invoice.getCandidate() != null) {
                debugInfo.put("candidateName", invoice.getCandidate().getName());
            }

            return ResponseEntity.ok()
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*")
                    .body(debugInfo);
        } catch (Exception e) {
            Map<String, Object> errorInfo = new HashMap<>();
            errorInfo.put("error", e.getMessage());
            errorInfo.put("type", e.getClass().getSimpleName());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*")
                    .body(errorInfo);
        }
    }

    /**
     * Generate PDF by invoice number with slashes - supports complex invoice numbers
     *
     * @param request The HTTP request to extract the full path
     * @return The PDF invoice as a downloadable resource
     */
    @GetMapping("/public/pdf/by-number/**")
    @Operation(summary = "Generate PDF by invoice number (with slashes)", description = "Generate a PDF invoice using invoice number that may contain slashes")
    public ResponseEntity<Resource> generatePdfByInvoiceNumberWithSlashes(HttpServletRequest request) {
        try {
            // Extract the invoice number from the full path
            String fullPath = request.getRequestURI();
            String basePath = "/api/invoice-generation/public/pdf/by-number/";
            String invoiceNumber = fullPath.substring(fullPath.indexOf(basePath) + basePath.length());

            // Decode URL-encoded invoice number
            String decodedInvoiceNumber = URLDecoder.decode(invoiceNumber, StandardCharsets.UTF_8);
            System.out.println("=== PDF Generation by Number (with slashes) ===");
            System.out.println("Full path: " + fullPath);
            System.out.println("Base path: " + basePath);
            System.out.println("Requested Invoice Number (encoded): " + invoiceNumber);
            System.out.println("Requested Invoice Number (decoded): " + decodedInvoiceNumber);

            // Validate input
            if (decodedInvoiceNumber == null || decodedInvoiceNumber.trim().isEmpty()) {
                System.err.println("ERROR: Invoice number is null or empty after decoding");
                return ResponseEntity.badRequest()
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "GET, POST, PUT, DELETE, OPTIONS")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "*")
                    .body(null);
            }

            // Use the new method that eagerly loads all related entities
            Invoice invoice = invoiceRepository.findByInvoiceNumberWithAllRelations(decodedInvoiceNumber)
                    .orElseThrow(() -> {
                        System.err.println("ERROR: Invoice not found with number: " + decodedInvoiceNumber);
                        return new ResourceNotFoundException("Invoice not found with number: " + decodedInvoiceNumber);
                    });

            System.out.println("✅ Found Invoice ID: " + invoice.getId());
            System.out.println("Invoice Client: " + (invoice.getClient() != null ? invoice.getClient().getName() : "NULL"));
            System.out.println("Invoice Project: " + (invoice.getProject() != null ? invoice.getProject().getName() : "NULL"));
            System.out.println("Invoice Candidate: " + (invoice.getCandidate() != null ? invoice.getCandidate().getName() : "NULL"));
            System.out.println("Invoice HSN Code: " + (invoice.getHsnCode() != null ? invoice.getHsnCode().getCode() : "NULL"));
            System.out.println("Invoice Redberyl Account: " + (invoice.getRedberylAccount() != null ? invoice.getRedberylAccount().getAccountName() : "NULL"));
            System.out.println("Invoice Billing Amount: " + invoice.getBillingAmount());
            System.out.println("Invoice Total Amount: " + invoice.getTotalAmount());

            // Validate critical fields before PDF generation
            if (invoice.getInvoiceNumber() == null || invoice.getInvoiceNumber().trim().isEmpty()) {
                System.err.println("ERROR: Invoice number is missing or empty");
                throw new IllegalStateException("Invoice number is missing or empty");
            }
            if (invoice.getBillingAmount() == null) {
                System.err.println("ERROR: Billing amount is missing");
                throw new IllegalStateException("Billing amount is missing");
            }

            System.out.println("🔄 Generating PDF...");
            Resource pdfResource = invoiceGenerationService.generatePdfInvoice(invoice);

            // Validate PDF resource
            if (pdfResource == null) {
                System.err.println("ERROR: Generated PDF resource is null");
                throw new IllegalStateException("Generated PDF resource is null");
            }

            try {
                long contentLength = pdfResource.contentLength();
                System.out.println("✅ PDF generated successfully, size: " + contentLength + " bytes");
                if (contentLength < 1000) {
                    System.err.println("WARNING: Generated PDF appears to be too small: " + contentLength + " bytes");
                }
            } catch (Exception e) {
                System.out.println("✅ PDF generated successfully (size check failed): " + e.getMessage());
            }

            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"invoice-" + invoice.getInvoiceNumber().replaceAll("[/\\\\]", "-") + ".pdf\"")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "GET, POST, PUT, DELETE, OPTIONS")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "*")
                    .body(pdfResource);
        } catch (Exception e) {
            System.err.println("Error generating PDF by invoice number (with slashes): " + e.getMessage());
            e.printStackTrace();
            throw new CustomException("Failed to generate PDF for invoice number: " + request.getRequestURI(), e);
        }
    }

    /**
     * Generate PDF by invoice number instead of ID - legacy endpoint for simple invoice numbers
     *
     * @param invoiceNumber The invoice number (e.g., INV-001)
     * @return The PDF invoice as a downloadable resource
     */
    @GetMapping("/public/pdf/by-number/{invoiceNumber}")
    @Operation(summary = "Generate PDF by invoice number (legacy)", description = "Generate a PDF invoice using simple invoice number")
    public ResponseEntity<Resource> generatePdfByInvoiceNumber(@PathVariable String invoiceNumber) {
        try {
            // Decode URL-encoded invoice number
            String decodedInvoiceNumber = URLDecoder.decode(invoiceNumber, StandardCharsets.UTF_8);
            System.out.println("=== PDF Generation by Number ===");
            System.out.println("Requested Invoice Number (encoded): " + invoiceNumber);
            System.out.println("Requested Invoice Number (decoded): " + decodedInvoiceNumber);

            // Use the new method that eagerly loads all related entities
            Invoice invoice = invoiceRepository.findByInvoiceNumberWithAllRelations(decodedInvoiceNumber)
                    .orElseThrow(() -> {
                        System.out.println("Invoice not found with number: " + decodedInvoiceNumber);
                        return new ResourceNotFoundException("Invoice not found with number: " + decodedInvoiceNumber);
                    });

            System.out.println("Found Invoice ID: " + invoice.getId());
            System.out.println("Invoice Client: " + (invoice.getClient() != null ? invoice.getClient().getName() : "NULL"));
            System.out.println("Invoice Project: " + (invoice.getProject() != null ? invoice.getProject().getName() : "NULL"));
            System.out.println("Invoice Candidate: " + (invoice.getCandidate() != null ? invoice.getCandidate().getName() : "NULL"));
            System.out.println("Invoice HSN Code: " + (invoice.getHsnCode() != null ? invoice.getHsnCode().getCode() : "NULL"));
            System.out.println("Invoice Redberyl Account: " + (invoice.getRedberylAccount() != null ? invoice.getRedberylAccount().getAccountName() : "NULL"));

            Resource pdfResource = invoiceGenerationService.generatePdfInvoice(invoice);

            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"invoice-" + invoice.getInvoiceNumber().replaceAll("[/\\\\]", "-") + ".pdf\"")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "GET, POST, PUT, DELETE, OPTIONS")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "*")
                    .body(pdfResource);
        } catch (Exception e) {
            System.err.println("Error generating PDF by invoice number: " + e.getMessage());
            e.printStackTrace();
            throw new CustomException("Failed to generate PDF for invoice number: " + invoiceNumber, e);
        }
    }

    /**
     * Debug endpoint to test invoice lookup with slashes
     *
     * @param request The HTTP request to extract the full path
     * @return Debug information about the invoice
     */
    @GetMapping("/debug/**")
    @Operation(summary = "Debug invoice lookup (with slashes)", description = "Debug endpoint to test invoice lookup for invoice numbers with slashes")
    public ResponseEntity<Map<String, Object>> debugInvoiceLookupWithSlashes(HttpServletRequest request) {
        try {
            // Extract the invoice number from the full path
            String fullPath = request.getRequestURI();
            String basePath = "/api/invoice-generation/debug/";
            String invoiceId = fullPath.substring(fullPath.indexOf(basePath) + basePath.length());

            // Decode URL-encoded invoice number
            String decodedInvoiceId = URLDecoder.decode(invoiceId, StandardCharsets.UTF_8);

            Map<String, Object> debugInfo = new HashMap<>();
            debugInfo.put("fullPath", fullPath);
            debugInfo.put("extractedInvoiceId", invoiceId);
            debugInfo.put("decodedInvoiceId", decodedInvoiceId);
            debugInfo.put("timestamp", System.currentTimeMillis());

            System.out.println("=== DEBUG INVOICE LOOKUP (WITH SLASHES) ===");
            System.out.println("Full path: " + fullPath);
            System.out.println("Extracted Invoice ID: " + invoiceId);
            System.out.println("Decoded Invoice ID: " + decodedInvoiceId);

            // Try to find the invoice
            try {
                Invoice invoice = invoiceRepository.findByInvoiceNumberWithAllRelations(decodedInvoiceId)
                        .orElse(null);

                if (invoice != null) {
                    debugInfo.put("invoiceFound", true);
                    debugInfo.put("invoiceDatabaseId", invoice.getId());
                    debugInfo.put("invoiceNumber", invoice.getInvoiceNumber());
                    debugInfo.put("clientName", invoice.getClient() != null ? invoice.getClient().getName() : "NULL");
                    debugInfo.put("projectName", invoice.getProject() != null ? invoice.getProject().getName() : "NULL");
                    debugInfo.put("candidateName", invoice.getCandidate() != null ? invoice.getCandidate().getName() : "NULL");
                    debugInfo.put("billingAmount", invoice.getBillingAmount());
                    debugInfo.put("totalAmount", invoice.getTotalAmount());
                    debugInfo.put("status", invoice.getStatus());

                    System.out.println("✅ Invoice found: " + invoice.getInvoiceNumber() + " (ID: " + invoice.getId() + ")");
                } else {
                    debugInfo.put("invoiceFound", false);
                    debugInfo.put("error", "Invoice not found with number: " + decodedInvoiceId);

                    System.out.println("❌ Invoice not found with number: " + decodedInvoiceId);
                }
            } catch (Exception e) {
                debugInfo.put("invoiceFound", false);
                debugInfo.put("error", "Error finding invoice: " + e.getMessage());
                e.printStackTrace();
            }

            return ResponseEntity.ok()
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "GET, POST, PUT, DELETE, OPTIONS")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "*")
                    .body(debugInfo);
        } catch (Exception e) {
            Map<String, Object> errorInfo = new HashMap<>();
            errorInfo.put("error", "Debug lookup failed: " + e.getMessage());
            errorInfo.put("timestamp", System.currentTimeMillis());
            e.printStackTrace();

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "GET, POST, PUT, DELETE, OPTIONS")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "*")
                    .body(errorInfo);
        }
    }

    /**
     * Debug endpoint to test ID extraction logic - legacy endpoint
     *
     * @param invoiceId The invoice ID to test
     * @return Debug information
     */
    @GetMapping("/debug/test-id/{invoiceId}")
    @Operation(summary = "Test ID extraction (legacy)", description = "Debug endpoint to test ID extraction logic")
    public ResponseEntity<String> testIdExtraction(@PathVariable String invoiceId) {
        StringBuilder result = new StringBuilder();
        result.append("Testing ID extraction for: ").append(invoiceId).append("\n");

        // Test different extraction patterns
        if (invoiceId.matches("INV-(\\d+)$")) {
            String extracted = invoiceId.replaceAll("INV-(\\d+)$", "$1");
            result.append("Pattern INV-(\\d+)$: ").append(extracted).append("\n");
        }

        if (invoiceId.matches("INV-\\d+-(\\d+)$")) {
            String extracted = invoiceId.replaceAll("INV-\\d+-(\\d+)$", "$1");
            result.append("Pattern INV-\\d+-(\\d+)$: ").append(extracted).append("\n");
        }

        if (invoiceId.matches(".*(\\d+)$")) {
            String extracted = invoiceId.replaceAll(".*(\\d+)$", "$1");
            result.append("Pattern .*(\\d+)$: ").append(extracted).append("\n");
        }

        // Test database lookup
        try {
            Long numericId = Long.parseLong(invoiceId.replaceAll(".*?(\\d+)$", "$1"));
            Invoice byId = invoiceRepository.findById(numericId).orElse(null);
            result.append("Database lookup by ID ").append(numericId).append(": ")
                  .append(byId != null ? byId.getInvoiceNumber() : "NOT FOUND").append("\n");
        } catch (Exception e) {
            result.append("Error parsing numeric ID: ").append(e.getMessage()).append("\n");
        }

        // Test by invoice number
        Invoice byNumber = invoiceRepository.findByInvoiceNumber(invoiceId).orElse(null);
        result.append("Database lookup by invoice number ").append(invoiceId).append(": ")
              .append(byNumber != null ? "FOUND (ID: " + byNumber.getId() + ")" : "NOT FOUND").append("\n");

        return ResponseEntity.ok(result.toString());
    }

    /**
     * Create sample invoices to match frontend data
     */
    @PostMapping("/debug/create-sample-invoices")
    @Operation(summary = "Create sample invoices", description = "Create sample invoices to match frontend data")
    public ResponseEntity<String> createSampleInvoices() {
        try {
            StringBuilder result = new StringBuilder("Creating sample invoices:\n");

            // Check if invoices already exist
            List<Invoice> existingInvoices = invoiceRepository.findAll();
            result.append("Existing invoices: ").append(existingInvoices.size()).append("\n");

            for (Invoice inv : existingInvoices) {
                result.append("- ID: ").append(inv.getId())
                      .append(", Number: ").append(inv.getInvoiceNumber())
                      .append(", Client: ").append(inv.getClient() != null ? inv.getClient().getName() : "NULL")
                      .append("\n");
            }

            // Create missing invoices if they don't exist
            String[] invoiceNumbers = {"INV-001", "INV-004", "INV-005"};

            for (String invoiceNumber : invoiceNumbers) {
                Invoice existing = invoiceRepository.findByInvoiceNumber(invoiceNumber).orElse(null);
                if (existing == null) {
                    result.append("Creating invoice: ").append(invoiceNumber).append("\n");

                    Invoice newInvoice = new Invoice();
                    newInvoice.setInvoiceNumber(invoiceNumber);
                    newInvoice.setInvoiceDate(LocalDate.now());
                    newInvoice.setDueDate(LocalDate.now().plusDays(30));
                    newInvoice.setBillingAmount(new BigDecimal("30600.00"));
                    newInvoice.setTaxAmount(new BigDecimal("5400.00"));
                    newInvoice.setTotalAmount(new BigDecimal("35400.00"));
                    // Note: Invoice entity doesn't have status field

                    // Try to associate with existing clients, projects, candidates if available
                    try {
                        // This is just for testing - in real scenario, these would be properly linked
                        result.append("Note: Invoice created without relations. You'll need to link it to client/project/candidate manually.\n");
                    } catch (Exception e) {
                        result.append("Warning: Could not link relations: ").append(e.getMessage()).append("\n");
                    }

                    Invoice saved = invoiceRepository.save(newInvoice);
                    result.append("Created invoice with ID: ").append(saved.getId()).append("\n");
                } else {
                    result.append("Invoice ").append(invoiceNumber).append(" already exists with ID: ").append(existing.getId()).append("\n");
                }
            }

            return ResponseEntity.ok(result.toString());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error creating sample invoices: " + e.getMessage());
        }
    }

    /**
     * Create complete sample data with all relations
     */
    @PostMapping("/debug/create-complete-sample-data")
    @Operation(summary = "Create complete sample data", description = "Create complete sample data with all relations")
    public ResponseEntity<String> createCompleteSampleData() {
        try {
            StringBuilder result = new StringBuilder("Creating complete sample data:\n");

            // 1. Create RedBeryl Account if it doesn't exist
            RedberylAccount redberylAccount = redberylAccountRepository.findAll().stream().findFirst().orElse(null);
            if (redberylAccount == null) {
                redberylAccount = new RedberylAccount();
                redberylAccount.setAccountName("RedBeryl Tech Solutions Pvt Ltd.");
                redberylAccount.setAccountNo("**************");
                redberylAccount.setBankName("HDFC Bank Ltd.");
                redberylAccount.setIfscCode("HDFC0000486");
                redberylAccount.setBranchName("Destination Centre, Magarpatta, Pune");
                redberylAccount.setAccountType("Current Account");
                redberylAccount.setGstn("27**********1Z5");
                redberylAccount.setCin("U72900PN2022PTC213381");
                redberylAccount.setPanNo("**********");
                redberylAccount = redberylAccountRepository.save(redberylAccount);
                result.append("Created RedBeryl Account with ID: ").append(redberylAccount.getId()).append("\n");
            } else {
                result.append("RedBeryl Account already exists with ID: ").append(redberylAccount.getId()).append("\n");
            }

            // 2. Create HSN Code if it doesn't exist
            HsnCode hsnCode = hsnCodeRepository.findAll().stream().findFirst().orElse(null);
            if (hsnCode == null) {
                hsnCode = new HsnCode();
                hsnCode.setCode("998313");
                hsnCode.setDescription("IT consulting services");
                hsnCode = hsnCodeRepository.save(hsnCode);
                result.append("Created HSN Code with ID: ").append(hsnCode.getId()).append("\n");
            } else {
                result.append("HSN Code already exists with ID: ").append(hsnCode.getId()).append("\n");
            }

            // 3. Get existing clients, projects, candidates
            List<Client> clients = clientRepository.findAll();
            List<Project> projects = projectRepository.findAll();
            List<Candidate> candidates = candidateRepository.findAll();

            result.append("Found ").append(clients.size()).append(" clients, ")
                  .append(projects.size()).append(" projects, ")
                  .append(candidates.size()).append(" candidates\n");

            // 4. Update existing invoices with proper relations
            String[] invoiceNumbers = {"INV-001", "INV-004", "INV-005"};

            for (int i = 0; i < invoiceNumbers.length; i++) {
                String invoiceNumber = invoiceNumbers[i];
                Invoice invoice = invoiceRepository.findByInvoiceNumber(invoiceNumber).orElse(null);

                if (invoice == null) {
                    // Create new invoice
                    invoice = new Invoice();
                    invoice.setInvoiceNumber(invoiceNumber);
                    invoice.setInvoiceDate(LocalDate.now());
                    invoice.setDueDate(LocalDate.now().plusDays(30));
                    invoice.setBillingAmount(new BigDecimal("30600.00"));
                    invoice.setTaxAmount(new BigDecimal("5400.00"));
                    invoice.setTotalAmount(new BigDecimal("35400.00"));
                    result.append("Created new invoice: ").append(invoiceNumber).append("\n");
                } else {
                    result.append("Updating existing invoice: ").append(invoiceNumber).append("\n");
                }

                // Link to RedBeryl Account
                invoice.setRedberylAccount(redberylAccount);

                // Link to HSN Code
                invoice.setHsnCode(hsnCode);

                // Link to client if available
                if (!clients.isEmpty() && i < clients.size()) {
                    invoice.setClient(clients.get(i));
                    result.append("  - Linked to client: ").append(clients.get(i).getName()).append("\n");
                }

                // Link to project if available
                if (!projects.isEmpty() && i < projects.size()) {
                    invoice.setProject(projects.get(i));
                    result.append("  - Linked to project: ").append(projects.get(i).getName()).append("\n");
                }

                // Link to candidate if available
                if (!candidates.isEmpty() && i < candidates.size()) {
                    invoice.setCandidate(candidates.get(i));
                    result.append("  - Linked to candidate: ").append(candidates.get(i).getName()).append("\n");
                }

                invoice = invoiceRepository.save(invoice);
                result.append("  - Saved invoice with ID: ").append(invoice.getId()).append("\n");
            }

            result.append("\nSample data creation completed successfully!\n");
            result.append("All invoices now have proper relations to RedBeryl Account, HSN Code, and other entities.\n");

            return ResponseEntity.ok(result.toString());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error creating complete sample data: " + e.getMessage());
        }
    }

    /**
     * Debug specific invoice relations
     */
    @GetMapping("/debug/invoice/{invoiceNumber}")
    @Operation(summary = "Debug invoice relations", description = "Check specific invoice and its relations")
    public ResponseEntity<String> debugInvoiceRelations(@PathVariable String invoiceNumber) {
        try {
            StringBuilder result = new StringBuilder("=== DEBUGGING INVOICE: " + invoiceNumber + " ===\n\n");

            Invoice invoice = invoiceRepository.findByInvoiceNumber(invoiceNumber).orElse(null);
            if (invoice == null) {
                return ResponseEntity.notFound().build();
            }

            result.append("Invoice ID: ").append(invoice.getId()).append("\n");
            result.append("Invoice Number: ").append(invoice.getInvoiceNumber()).append("\n");
            result.append("Invoice Date: ").append(invoice.getInvoiceDate()).append("\n");
            result.append("Total Amount: ").append(invoice.getTotalAmount()).append("\n\n");

            // Check RedBeryl Account
            result.append("=== REDBERYL ACCOUNT ===\n");
            if (invoice.getRedberylAccount() != null) {
                RedberylAccount account = invoice.getRedberylAccount();
                result.append("✓ RedBeryl Account is linked!\n");
                result.append("Account ID: ").append(account.getId()).append("\n");
                result.append("Account Name: ").append(account.getAccountName()).append("\n");
                result.append("Account No: ").append(account.getAccountNo()).append("\n");
                result.append("Bank Name: ").append(account.getBankName()).append("\n");
                result.append("IFSC Code: ").append(account.getIfscCode()).append("\n");
                result.append("Branch Name: ").append(account.getBranchName()).append("\n");
                result.append("Account Type: ").append(account.getAccountType()).append("\n");
                result.append("GSTN: ").append(account.getGstn()).append("\n");
                result.append("CIN: ").append(account.getCin()).append("\n");
                result.append("PAN No: ").append(account.getPanNo()).append("\n");
            } else {
                result.append("✗ NO RedBeryl Account linked!\n");
            }

            // Check HSN Code
            result.append("\n=== HSN CODE ===\n");
            if (invoice.getHsnCode() != null) {
                HsnCode hsn = invoice.getHsnCode();
                result.append("✓ HSN Code is linked!\n");
                result.append("HSN ID: ").append(hsn.getId()).append("\n");
                result.append("HSN Code: ").append(hsn.getCode()).append("\n");
                result.append("HSN Description: ").append(hsn.getDescription()).append("\n");
            } else {
                result.append("✗ NO HSN Code linked!\n");
            }

            // Check Client
            result.append("\n=== CLIENT ===\n");
            if (invoice.getClient() != null) {
                Client client = invoice.getClient();
                result.append("✓ Client is linked!\n");
                result.append("Client ID: ").append(client.getId()).append("\n");
                result.append("Client Name: ").append(client.getName()).append("\n");
            } else {
                result.append("✗ NO Client linked!\n");
            }

            // Check Project
            result.append("\n=== PROJECT ===\n");
            if (invoice.getProject() != null) {
                Project project = invoice.getProject();
                result.append("✓ Project is linked!\n");
                result.append("Project ID: ").append(project.getId()).append("\n");
                result.append("Project Name: ").append(project.getName()).append("\n");
            } else {
                result.append("✗ NO Project linked!\n");
            }

            // Check Candidate
            result.append("\n=== CANDIDATE ===\n");
            if (invoice.getCandidate() != null) {
                Candidate candidate = invoice.getCandidate();
                result.append("✓ Candidate is linked!\n");
                result.append("Candidate ID: ").append(candidate.getId()).append("\n");
                result.append("Candidate Name: ").append(candidate.getName()).append("\n");
            } else {
                result.append("✗ NO Candidate linked!\n");
            }

            return ResponseEntity.ok(result.toString());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error debugging invoice: " + e.getMessage());
        }
    }

    /**
     * Test PDF generation for a specific invoice number (supports slashes)
     */
    @GetMapping("/test/pdf/**")
    @Operation(summary = "Test PDF generation", description = "Test PDF generation for a specific invoice number with slashes")
    public ResponseEntity<Resource> testPdfGenerationWithSlashes(HttpServletRequest request) {
        try {
            // Extract the invoice number from the full path
            String fullPath = request.getRequestURI();
            String basePath = "/api/invoice-generation/test/pdf/";
            String invoiceNumber = fullPath.substring(fullPath.indexOf(basePath) + basePath.length());

            // Decode URL-encoded invoice number
            String decodedInvoiceNumber = URLDecoder.decode(invoiceNumber, StandardCharsets.UTF_8);

            System.out.println("=== TEST PDF GENERATION ===");
            System.out.println("Full path: " + fullPath);
            System.out.println("Invoice Number (encoded): " + invoiceNumber);
            System.out.println("Invoice Number (decoded): " + decodedInvoiceNumber);

            // Find the invoice
            Invoice invoice = invoiceRepository.findByInvoiceNumberWithAllRelations(decodedInvoiceNumber)
                    .orElseThrow(() -> new ResourceNotFoundException("Invoice not found with number: " + decodedInvoiceNumber));

            System.out.println("✅ Found invoice with ID: " + invoice.getId());

            // Generate PDF
            System.out.println("🔄 Generating PDF...");
            Resource pdfResource = invoiceGenerationService.generatePdfInvoice(invoice);

            try {
                System.out.println("✅ PDF generated successfully, size: " + pdfResource.contentLength() + " bytes");
            } catch (Exception e) {
                System.out.println("✅ PDF generated successfully (size check failed): " + e.getMessage());
            }

            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_PDF)
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"test-invoice-" + decodedInvoiceNumber.replaceAll("[/\\\\]", "-") + ".pdf\"")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "GET, POST, PUT, DELETE, OPTIONS")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "*")
                    .body(pdfResource);
        } catch (Exception e) {
            System.err.println("❌ Error in test PDF generation: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*")
                    .body(null);
        }
    }

    /**
     * Test HTML generation for a specific invoice number (supports slashes)
     */
    @GetMapping("/test/html/**")
    @Operation(summary = "Test HTML generation", description = "Test HTML generation for a specific invoice number with slashes")
    public ResponseEntity<String> testHtmlGenerationWithSlashes(HttpServletRequest request) {
        try {
            // Extract the invoice number from the full path
            String fullPath = request.getRequestURI();
            String basePath = "/api/invoice-generation/test/html/";
            String invoiceNumber = fullPath.substring(fullPath.indexOf(basePath) + basePath.length());

            // Decode URL-encoded invoice number
            String decodedInvoiceNumber = URLDecoder.decode(invoiceNumber, StandardCharsets.UTF_8);

            System.out.println("=== TEST HTML GENERATION ===");
            System.out.println("Full path: " + fullPath);
            System.out.println("Invoice Number (encoded): " + invoiceNumber);
            System.out.println("Invoice Number (decoded): " + decodedInvoiceNumber);

            // Find the invoice
            Invoice invoice = invoiceRepository.findByInvoiceNumberWithAllRelations(decodedInvoiceNumber)
                    .orElseThrow(() -> new ResourceNotFoundException("Invoice not found with number: " + decodedInvoiceNumber));

            System.out.println("✅ Found invoice with ID: " + invoice.getId());

            // Generate HTML
            System.out.println("🔄 Generating HTML...");
            String htmlContent = invoiceGenerationService.generateHtmlInvoice(invoice);

            System.out.println("✅ HTML generated successfully, length: " + htmlContent.length() + " characters");

            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_HTML)
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "GET, POST, PUT, DELETE, OPTIONS")
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "*")
                    .body(htmlContent);
        } catch (Exception e) {
            System.err.println("❌ Error in test HTML generation: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .header(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*")
                    .body("Error: " + e.getMessage());
        }
    }

    /**
     * Force link invoice to RedBeryl account
     */
    @PostMapping("/debug/force-link/{invoiceNumber}")
    @Operation(summary = "Force link invoice to RedBeryl account", description = "Force link specific invoice to RedBeryl account")
    public ResponseEntity<String> forceLinkInvoice(@PathVariable String invoiceNumber) {
        try {
            StringBuilder result = new StringBuilder("=== FORCE LINKING INVOICE: " + invoiceNumber + " ===\n\n");

            // Find the invoice
            Invoice invoice = invoiceRepository.findByInvoiceNumber(invoiceNumber).orElse(null);
            if (invoice == null) {
                return ResponseEntity.notFound().build();
            }

            // Find or create RedBeryl account
            RedberylAccount redberylAccount = redberylAccountRepository.findAll().stream().findFirst().orElse(null);
            if (redberylAccount == null) {
                redberylAccount = new RedberylAccount();
                redberylAccount.setAccountName("RedBeryl Tech Solutions Pvt Ltd.");
                redberylAccount.setAccountNo("**************");
                redberylAccount.setBankName("HDFC Bank Ltd.");
                redberylAccount.setIfscCode("HDFC0000486");
                redberylAccount.setBranchName("Destination Centre, Magarpatta, Pune");
                redberylAccount.setAccountType("Current Account");
                redberylAccount.setGstn("27**********1Z5");
                redberylAccount.setCin("U72900PN2022PTC213381");
                redberylAccount.setPanNo("**********");
                redberylAccount = redberylAccountRepository.save(redberylAccount);
                result.append("Created new RedBeryl Account with ID: ").append(redberylAccount.getId()).append("\n");
            } else {
                result.append("Found existing RedBeryl Account with ID: ").append(redberylAccount.getId()).append("\n");
            }

            // Find or create HSN code
            HsnCode hsnCode = hsnCodeRepository.findAll().stream().findFirst().orElse(null);
            if (hsnCode == null) {
                hsnCode = new HsnCode();
                hsnCode.setCode("998313");
                hsnCode.setDescription("IT consulting services");
                hsnCode = hsnCodeRepository.save(hsnCode);
                result.append("Created new HSN Code with ID: ").append(hsnCode.getId()).append("\n");
            } else {
                result.append("Found existing HSN Code with ID: ").append(hsnCode.getId()).append("\n");
            }

            // Link the invoice to RedBeryl account and HSN code
            invoice.setRedberylAccount(redberylAccount);
            invoice.setHsnCode(hsnCode);

            // Save the invoice
            invoice = invoiceRepository.save(invoice);

            result.append("\n=== LINKING COMPLETED ===\n");
            result.append("Invoice ").append(invoiceNumber).append(" has been linked to:\n");
            result.append("- RedBeryl Account: ").append(redberylAccount.getAccountName()).append("\n");
            result.append("- HSN Code: ").append(hsnCode.getCode()).append("\n");
            result.append("\nNow try generating the PDF again!\n");

            return ResponseEntity.ok(result.toString());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error force linking invoice: " + e.getMessage());
        }
    }

    /**
     * Simple fix for INV-005 RedBeryl account issue
     */
    @GetMapping("/debug/fix-inv-005")
    @Operation(summary = "Fix INV-005 RedBeryl account", description = "Simple fix for INV-005 RedBeryl account issue")
    public ResponseEntity<String> fixInv005() {
        try {
            StringBuilder result = new StringBuilder("=== FIXING INV-005 REDBERYL ACCOUNT ISSUE ===\n\n");

            // 1. Find INV-005
            Invoice invoice = invoiceRepository.findByInvoiceNumber("INV-005").orElse(null);
            if (invoice == null) {
                return ResponseEntity.badRequest().body("INV-005 not found!");
            }
            result.append("✓ Found INV-005 with ID: ").append(invoice.getId()).append("\n");

            // 2. Create RedBeryl Account
            RedberylAccount account = new RedberylAccount();
            account.setAccountName("RedBeryl Tech Solutions Pvt Ltd.");
            account.setAccountNo("**************");
            account.setBankName("HDFC Bank Ltd.");
            account.setIfscCode("HDFC0000486");
            account.setBranchName("Destination Centre, Magarpatta, Pune");
            account.setAccountType("Current Account");
            account.setGstn("27**********1Z5");
            account.setCin("U72900PN2022PTC213381");
            account.setPanNo("**********");

            account = redberylAccountRepository.save(account);
            result.append("✓ Created RedBeryl Account with ID: ").append(account.getId()).append("\n");

            // 3. Create HSN Code
            HsnCode hsnCode = new HsnCode();
            hsnCode.setCode("998313");
            hsnCode.setDescription("IT consulting services");
            hsnCode = hsnCodeRepository.save(hsnCode);
            result.append("✓ Created HSN Code with ID: ").append(hsnCode.getId()).append("\n");

            // 4. Link to invoice
            invoice.setRedberylAccount(account);
            invoice.setHsnCode(hsnCode);
            invoice = invoiceRepository.save(invoice);
            result.append("✓ Linked INV-005 to RedBeryl Account and HSN Code\n");

            result.append("\n=== SUCCESS! ===\n");
            result.append("INV-005 is now properly linked to:\n");
            result.append("- RedBeryl Account: ").append(account.getAccountName()).append("\n");
            result.append("- Account Number: ").append(account.getAccountNo()).append("\n");
            result.append("- Bank: ").append(account.getBankName()).append("\n");
            result.append("- HSN Code: ").append(hsnCode.getCode()).append("\n");
            result.append("\nNow generate the PDF again - it should show real data!\n");

            return ResponseEntity.ok(result.toString());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error fixing INV-005: " + e.getMessage() + "\nStack trace: " + java.util.Arrays.toString(e.getStackTrace()));
        }
    }
}
